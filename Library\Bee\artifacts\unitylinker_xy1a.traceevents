{ "pid": 28380, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 28380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 28380, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 28380, "tid": 12884901888, "ts": 1752054334533864, "dur": 336495, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 28380, "tid": 1, "ts": 1752054346362237, "dur": 5000, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 28380, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 28380, "tid": 1, "ts": 1752054346367240, "dur": 1, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 28380, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 28380, "tid": 4294967296, "ts": 1752054334540675, "dur": 179550, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 28380, "tid": 1, "ts": 1752054346367242, "dur": 107, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 28380, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 28380, "tid": 1, "ts": 1752054327416928, "dur": 18915953, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 28380, "tid": 1, "ts": 1752054327420389, "dur": 191265, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054327440591, "dur": 121948, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054327611657, "dur": 30009, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054327700014, "dur": 151951, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054327855763, "dur": 212175, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054328068029, "dur": 3304627, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054331372705, "dur": 718541, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332091273, "dur": 492712, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332584011, "dur": 107134, "ph": "X", "name": "ResolveForEngineModuleStrippingDisabledStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332691157, "dur": 23030, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332714195, "dur": 3022, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332717221, "dur": 5482, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332722707, "dur": 653, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332723374, "dur": 23870, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332747262, "dur": 624, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332747890, "dur": 825, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332748718, "dur": 407, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332749139, "dur": 433, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332749575, "dur": 490, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332750067, "dur": 373, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332750442, "dur": 396, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332750841, "dur": 1155, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332751998, "dur": 31303, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332783310, "dur": 2083, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332785415, "dur": 21917, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332807356, "dur": 14500, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332821866, "dur": 6614, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332828490, "dur": 4350, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332832851, "dur": 12215, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332845077, "dur": 8843, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332853925, "dur": 3641, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332857569, "dur": 2286, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332859857, "dur": 2504, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332862364, "dur": 2001, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332864384, "dur": 3488, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332867883, "dur": 959, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332868851, "dur": 1887, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332870747, "dur": 73333, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332944090, "dur": 406, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332944499, "dur": 528, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332945029, "dur": 10065, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332955100, "dur": 6957, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332962062, "dur": 3430, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054332965522, "dur": 46236, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333011765, "dur": 13837, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333025616, "dur": 256906, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333282544, "dur": 931, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333283485, "dur": 1062, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333284565, "dur": 530, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333285108, "dur": 1913, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333287030, "dur": 1461, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333288501, "dur": 6433, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333294939, "dur": 288, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333295228, "dur": 292, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333295521, "dur": 142, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333295664, "dur": 28, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333295698, "dur": 182101, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333477823, "dur": 320568, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333798410, "dur": 550, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054333798968, "dur": 209380, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054334008362, "dur": 48687, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054334057066, "dur": 11164, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054334068244, "dur": 36116, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054334104379, "dur": 35468, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054334139864, "dur": 5047014, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339186903, "dur": 1472, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339188387, "dur": 10097, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339198494, "dur": 136227, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339334745, "dur": 10884, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339345658, "dur": 9555, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339355235, "dur": 5532, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339360779, "dur": 5611, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339366401, "dur": 7139, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339373550, "dur": 352, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339373921, "dur": 838, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339374767, "dur": 931, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054339375705, "dur": 6855712, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054346231445, "dur": 39198, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054346270687, "dur": 697, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054346277334, "dur": 55203, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054346332885, "dur": 14396, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054346367351, "dur": 235, "ph": "X", "name": "", "args": {} },
{ "pid": 28380, "tid": 1, "ts": 1752054346361193, "dur": 7065, "ph": "X", "name": "Write chrome-trace events", "args": {} },
