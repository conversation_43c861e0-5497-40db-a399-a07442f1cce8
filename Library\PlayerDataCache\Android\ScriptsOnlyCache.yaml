ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp:
    - RCC_AssetPaths
    - RCC_Camera
    - RCC_CarControllerV4
    - RCC_ChangableWheels
    - RCC_CinematicCamera
    - RCC_ColorPickerBySliders
    - RCC_CustomizationSetups
    - RCC_Customizer
    - RCC_Customizer_Brake
    - RCC_Customizer_CustomizationManager
    - RCC_Customizer_Decal
    - RCC_Customizer_DecalManager
    - RCC_Customizer_Engine
    - RCC_Customizer_Handling
    - RCC_Customizer_Neon
    - RCC_Customizer_NeonManager
    - RCC_Customizer_Paint
    - RCC_Customizer_PaintManager
    - RCC_Customizer_Siren
    - RCC_Customizer_SirenManager
    - RCC_Customizer_Speed
    - RCC_Customizer_Spoiler
    - RCC_Customizer_SpoilerManager
    - RCC_Customizer_UpgradeManager
    - RCC_Customizer_WheelManager
    - RCC_DashboardColors
    - RCC_DashboardInputs
    - RCC_DashboardObjects
    - RCC_Demo
    - RCC_DemoMaterials
    - RCC_DemoVehicles
    - RCC_DetachablePart
    - RCC_Exhaust
    - RCC_FOVForCinematicCamera
    - RCC_GroundMaterials
    - RCC_HoodCamera
    - RCC_InfoLabel
    - RCC_InitialSettings
    - RCC_Light
    - RCC_Mirror
    - RCC_MobileButtons
    - RCC_PoliceSiren
    - RCC_Records
    - RCC_Settings
    - RCC_Skidmarks
    - RCC_SkidmarksManager
    - RCC_Telemetry
    - RCC_Teleporter
    - RCC_TrailerAttachPoint
    - RCC_TruckTrailer
    - RCC_UI_Canvas_Customizer
    - RCC_UI_Color
    - RCC_UI_Controller
    - RCC_UI_CustomizationSlider
    - RCC_UI_DashboardButton
    - RCC_UI_DashboardDisplay
    - RCC_UI_Decal
    - RCC_UI_DecalSetLocation
    - RCC_UI_Drag
    - RCC_UI_Joystick
    - RCC_UI_Neon
    - RCC_UI_ShowroomCameraDrag
    - RCC_UI_Siren
    - RCC_UI_SliderTextReader
    - RCC_UI_Spoiler
    - RCC_UI_SteeringWheelController
    - RCC_UI_Upgrade
    - RCC_UI_Wheel
    - RCC_Useless
    - RCC_WheelCamera
    - RCC_WheelCollider
    Assembly-CSharp-Editor.dll:
    - Invector.vCharacterController.vEditorStartupPrefs
    Assembly-CSharp-firstpass.dll:
    - DG.Tweening.DOTweenAnimation
    Assembly-CSharp.dll:
    - AdvancedHelicopterControllerwithShooting.BulletScript
    - AdvancedHelicopterControllerwithShooting.FollowTargetCamera
    - AdvancedHelicopterControllerwithShooting.GameCanvas
    - AdvancedHelicopterControllerwithShooting.Gasoline
    - AdvancedHelicopterControllerwithShooting.GunController
    - AdvancedHelicopterControllerwithShooting.GyroscopeViewController
    - AdvancedHelicopterControllerwithShooting.HeliRotorController
    - AdvancedHelicopterControllerwithShooting.HelicopterController
    - AdvancedHelicopterControllerwithShooting.HelicopterSystemManager
    - AdvancedHelicopterControllerwithShooting.MissileScript
    - AdvancedHelicopterControllerwithShooting.RadarSystem
    - AdvancedHelicopterControllerwithShooting.SimpleJoystick
    - AirplaneControl
    - AirplaneCrash
    - BikeAnimation
    - BikeCamera
    - BikeControl
    - BoatControllerwithShooting.BoatController
    - BoatControllerwithShooting.BoatEngineSound
    - BoatControllerwithShooting.BoatSystemManager
    - BoatControllerwithShooting.BulletScript
    - BoatControllerwithShooting.FollowTargetCamera
    - BoatControllerwithShooting.GameCanvas
    - BoatControllerwithShooting.Gasoline
    - BoatControllerwithShooting.GunController
    - BoatControllerwithShooting.MissileScript
    - BoatControllerwithShooting.RadarSystem
    - BoatControllerwithShooting.SimpleJoystick
    - BoatControllerwithShooting.SpeedometerScript
    - BoatControllerwithShooting.WaterEffect
    - BoatControllerwithShooting.WaterSplashScript
    - CameraManager
    - Drivevehicleenterexit
    - Dronecamera
    - Dronecameraactivedeactive
    - FixedJoystick
    - GizmoObject
    - Invector.Utils.vComment
    - Invector.vCharacterController.vThirdPersonController
    - Invector.vCharacterController.vThirdPersonInput
    - OtherVehicleEnterexit
    - RCC_AssetPaths
    - RCC_Camera
    - RCC_CarControllerV4
    - RCC_CinematicCamera
    - RCC_ColorPickerBySliders
    - RCC_DashboardColors
    - RCC_DashboardInputs
    - RCC_Demo
    - RCC_Exhaust
    - RCC_FOVForCinematicCamera
    - RCC_GroundMaterials
    - RCC_HoodCamera
    - RCC_InfoLabel
    - RCC_Light
    - RCC_Mirror
    - RCC_MobileButtons
    - RCC_SceneManager
    - RCC_Settings
    - RCC_Skidmarks
    - RCC_SkidmarksManager
    - RCC_Telemetry
    - RCC_Teleporter
    - RCC_TrailerAttachPoint
    - RCC_TruckTrailer
    - RCC_UI_Canvas_Customizer
    - RCC_UI_Color
    - RCC_UI_Controller
    - RCC_UI_CustomizationSlider
    - RCC_UI_DashboardButton
    - RCC_UI_DashboardDisplay
    - RCC_UI_Decal
    - RCC_UI_DecalSetLocation
    - RCC_UI_Drag
    - RCC_UI_Joystick
    - RCC_UI_Neon
    - RCC_UI_ShowroomCameraDrag
    - RCC_UI_Siren
    - RCC_UI_SliderTextReader
    - RCC_UI_Spoiler
    - RCC_UI_SteeringWheelController
    - RCC_UI_Upgrade
    - RCC_UI_Wheel
    - RCC_Useless
    - RCC_WheelCollider
    - RearLookAt
    - SMR_Theme
    - TrainInputSettings
    - VehiclePlayerPosition
    - WSMGameStudio.RailroadSystem.DemoUI_v3
    - WSMGameStudio.RailroadSystem.PassengerTags
    - WSMGameStudio.RailroadSystem.RailSensor
    - WSMGameStudio.RailroadSystem.RouteManager
    - WSMGameStudio.RailroadSystem.TrainAttachPassenger
    - WSMGameStudio.RailroadSystem.TrainCarCoupler
    - WSMGameStudio.RailroadSystem.TrainController_v3
    - WSMGameStudio.RailroadSystem.TrainDoor
    - WSMGameStudio.RailroadSystem.TrainDoorsController
    - WSMGameStudio.RailroadSystem.TrainPlayerInput
    - WSMGameStudio.RailroadSystem.TrainSpawner
    - WSMGameStudio.RailroadSystem.TrainSpeedMonitor
    - WSMGameStudio.RailroadSystem.TrainStationController
    - WSMGameStudio.RailroadSystem.TrainSuspension
    - WSMGameStudio.RailroadSystem.TrainWheel_v3
    - WSMGameStudio.RailroadSystem.TrainWheelsTruck
    - WSMGameStudio.RailroadSystem.Wagon_v3
    - WSMGameStudio.Splines.SMR_GeneratedCollider
    - WSMGameStudio.Splines.SMR_GeneratedMesh
    - WSMGameStudio.Splines.SMR_IgnoredObject
    - WSMGameStudio.Splines.SMR_MeshCapTag
    - WSMGameStudio.Splines.SMR_MeshGenerationProfile
    - WSMGameStudio.Splines.Spline
    - WSMGameStudio.Splines.SplineMeshRenderer
    - WheelManager
    - allcanvasfalse
    - truckrcccam
    - vThirdPersonCamera
    DOTween.dll:
    - DG.Tweening.Core.DOTweenSettings
    Unity.Cinemachine.dll:
    - Unity.Cinemachine.CinemachineBrain
    - Unity.Cinemachine.CinemachineCamera
    - Unity.Cinemachine.CinemachineDeoccluder
    - Unity.Cinemachine.CinemachineFollow
    - Unity.Cinemachine.CinemachineFreeLookModifier
    - Unity.Cinemachine.CinemachineInputAxisController
    - Unity.Cinemachine.CinemachineRotationComposer
    - Unity.Cinemachine.CinemachineThirdPersonFollow
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.UI.InputSystemUIInputModule
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputActionAsset
    - UnityEngine.InputSystem.InputActionReference
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    - UnityEngine.InputSystem.UI.InputSystemUIInputModule
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.LensFlareDataSRP
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerMessageBox
    - UnityEngine.Rendering.UI.DebugUIHandlerObject
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectList
    - UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerProgressBar
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerValueTuple
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    Unity.RenderPipelines.Core.Runtime.dll:
    - UnityEngine.Rendering.LensFlareDataSRP
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.Splines.Editor.dll:
    - UnityEditor.Splines.SelectionContext
    Unity.TextMeshPro:
    - TMPro.TMP_ColorGradient
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    Unity.TextMeshPro.dll:
    - TMPro.TMP_ColorGradient
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshProUGUI
    Unity.Timeline.Editor.dll:
    - TimelinePreferences
    - UnityEditor.Timeline.SequenceHierarchy
    - UnityEditor.Timeline.TimelineWindow
    Unity.Timeline.dll:
    - UnityEngine.Timeline.AnimationTrack
    - UnityEngine.Timeline.TimelineAsset
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.CommandLineTest.RunData
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.Dropdown
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.RawImage
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Slider
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.Dropdown
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.Mask
    - UnityEngine.UI.Outline
    - UnityEngine.UI.RawImage
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Shadow
    - UnityEngine.UI.Slider
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
  serializedClasses:
    Assembly-CSharp:
    - .BikeAnimation/IKPointsClass
    - .BikeCamera/BikeUIClass
    - .BikeControl/BikeLights
    - .BikeControl/BikeParticles
    - .BikeControl/BikeSetting
    - .BikeControl/BikeSounds
    - .BikeControl/BikeWheels
    - .BikeControl/ConnectWheel
    - .BikeControl/HitGround
    - .BikeControl/WheelSetting
    - .RCC_Camera/CameraTarget
    - .RCC_CarControllerV4/Gear
    - .RCC_ChangableWheels/ChangableWheels
    - .RCC_CustomizationData
    - .RCC_Customizer_Loadout
    - .RCC_Damage
    - .RCC_DashboardObjects/FuelDial
    - .RCC_DashboardObjects/HeatDial
    - .RCC_DashboardObjects/RPMDial
    - .RCC_DashboardObjects/SpeedoMeterDial
    - .RCC_Emission
    - .RCC_GroundMaterials/GroundMaterialFrictions
    - .RCC_GroundMaterials/TerrainFrictions
    - .RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes
    - .RCC_Inputs
    - .RCC_Settings/BehaviorType
    - .RCC_TruckTrailer/TrailerWheel
    - AdvancedHelicopterControllerwithShooting.RadarTypeInfo
    - BoatControllerwithShooting.RadarTypeInfo
    - Invector.vCharacterController.vThirdPersonMotor/vMovementSpeed
    - WSMGameStudio.RailroadSystem.Route
    - WSMGameStudio.RailroadSystem.Sensors
    DOTween:
    - DG.Tweening.Core.DOTweenSettings/ModulesSetup
    - DG.Tweening.Core.DOTweenSettings/SafeModeOptions
    Unity.Cinemachine:
    - Unity.Cinemachine.CameraTarget
    - Unity.Cinemachine.CinemachineBlendDefinition
    - Unity.Cinemachine.CinemachineBrain/LensModeOverrideSettings
    - Unity.Cinemachine.CinemachineDeoccluder/ObstacleAvoidance
    - Unity.Cinemachine.CinemachineDeoccluder/ObstacleAvoidance/FollowTargetSettings
    - Unity.Cinemachine.CinemachineDeoccluder/QualityEvaluation
    - Unity.Cinemachine.CinemachineInputAxisController/Reader
    - Unity.Cinemachine.CinemachineThirdPersonFollow/ObstacleSettings
    - Unity.Cinemachine.InputAxisControllerManager`1
    - Unity.Cinemachine.LensSettings
    - Unity.Cinemachine.LensSettings/PhysicalSettings
    - Unity.Cinemachine.LookaheadSettings
    - Unity.Cinemachine.PrioritySettings
    - Unity.Cinemachine.ScreenComposerSettings
    - Unity.Cinemachine.ScreenComposerSettings/DeadZoneSettings
    - Unity.Cinemachine.ScreenComposerSettings/HardLimitSettings
    - Unity.Cinemachine.TargetTracking.TrackerSettings
    Unity.InputSystem:
    - UnityEngine.InputSystem.InputAction
    - UnityEngine.InputSystem.InputActionMap
    - UnityEngine.InputSystem.InputBinding
    - UnityEngine.InputSystem.InputControlScheme
    - UnityEngine.InputSystem.InputControlScheme/DeviceRequirement
    Unity.RenderPipelines.Core.Runtime:
    - UnityEngine.Rendering.LensFlareDataElementSRP
    - UnityEngine.Rendering.SRPLensFlareBlendMode
    - UnityEngine.Rendering.SRPLensFlareColorType
    - UnityEngine.Rendering.SRPLensFlareDistribution
    - UnityEngine.Rendering.SRPLensFlareType
    - UnityEngine.Rendering.TextureGradient
    - UnityEngine.Rendering.UI.DebugUIPrefabBundle
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.TMP_Sprite
    - TMPro.TMP_SpriteCharacter
    - TMPro.TMP_SpriteGlyph
    - TMPro.TMP_Style
    - TMPro.VertexGradient
    Unity.Timeline:
    - UnityEngine.Timeline.MarkerList
    - UnityEngine.Timeline.TimelineAsset/EditorSettings
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    - UnityEngine.Events.UnityEvent
    - UnityEngine.RectOffset
    UnityEngine.IMGUIModule:
    - UnityEngine.GUISettings
    - UnityEngine.GUIStyle
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    - UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord
    - UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord
    - UnityEngine.TextCore.LowLevel.GlyphValueRecord
    UnityEngine.UI:
    - UnityEngine.EventSystems.EventTrigger/Entry
    - UnityEngine.EventSystems.EventTrigger/TriggerEvent
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.Dropdown/DropdownEvent
    - UnityEngine.UI.Dropdown/OptionData
    - UnityEngine.UI.Dropdown/OptionDataList
    - UnityEngine.UI.FontData
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.ScrollRect/ScrollRectEvent
    - UnityEngine.UI.Scrollbar/ScrollEvent
    - UnityEngine.UI.Slider/SliderEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
  methodsToPreserve:
  - assembly: Assembly-CSharp
    fullTypeName: Drivevehicleenterexit
    methodName: Exitvehicle
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: ExitTrain
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: EnterAirplane
  - assembly: Assembly-CSharp
    fullTypeName: Dronecameraactivedeactive
    methodName: PlayDrone
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: ExitAirplane
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: EnterMotorbike
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: EnterHelicopter
  - assembly: Assembly-CSharp
    fullTypeName: Drivevehicleenterexit
    methodName: Entervehicle
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: EnterTrain
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: EnterBoat
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: ExitHelicopter
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: ExitBoat
  - assembly: Assembly-CSharp
    fullTypeName: OtherVehicleEnterexit
    methodName: ExitMotorbike
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: Dronecameraactivedeactive
    methodName: StopDrone
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: BoatControllerwithShooting.GameCanvas
    methodName: Click_Button_MachineGun_Down
  - assembly: Assembly-CSharp
    fullTypeName: BoatControllerwithShooting.GameCanvas
    methodName: Click_Button_Guns_Up
  - assembly: Assembly-CSharp
    fullTypeName: BoatControllerwithShooting.GameCanvas
    methodName: Click_Button_Missle_Down
  - assembly: Assembly-CSharp
    fullTypeName: BoatControllerwithShooting.GameCanvas
    methodName: Click_Button_Guns_Up
  - assembly: Assembly-CSharp
    fullTypeName: BoatControllerwithShooting.GameCanvas
    methodName: Click_Button_CameraSwitch
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Upgrade
    methodName: OnClick
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: CameraManager
    methodName: ToggleCamera
  - assembly: Assembly-CSharp
    fullTypeName: AdvancedHelicopterControllerwithShooting.GameCanvas
    methodName: Click_Button_CameraSwitch
  - assembly: Assembly-CSharp
    fullTypeName: AdvancedHelicopterControllerwithShooting.GameCanvas
    methodName: Click_Button_Missle_Down
  - assembly: Assembly-CSharp
    fullTypeName: AdvancedHelicopterControllerwithShooting.GameCanvas
    methodName: Click_Button_Guns_Up
  - assembly: Assembly-CSharp
    fullTypeName: AdvancedHelicopterControllerwithShooting.GameCanvas
    methodName: Propeller_Height_Update
  - assembly: Assembly-CSharp
    fullTypeName: AdvancedHelicopterControllerwithShooting.GameCanvas
    methodName: Click_Button_MachineGun_Down
  - assembly: Assembly-CSharp
    fullTypeName: AdvancedHelicopterControllerwithShooting.GameCanvas
    methodName: Click_Button_Guns_Up
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Canvas_Customizer
    methodName: ChooseClass
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Upgrade
    methodName: OnClick
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_CustomizationSlider
    methodName: OnSlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_DecalSetLocation
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Neon
    methodName: Upgrade
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UI_Decal
    methodName: Upgrade
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerButton
    methodName: OnAction
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: OnScrollbarClicked
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnDecrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerObjectPopupField
    methodName: OnIncrement
  - assembly: Unity.RenderPipelines.Core.Runtime
    fullTypeName: UnityEngine.Rendering.UI.DebugUIHandlerPanel
    methodName: ResetDebugManager
  sceneClasses:
    Assets/Scenes/TPSFREEHAND.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 59
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 96
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 818}
    - Class: 114
      Script: {instanceID: 30788}
    - Class: 114
      Script: {instanceID: 30840}
    - Class: 114
      Script: {instanceID: 30850}
    - Class: 114
      Script: {instanceID: 30856}
    - Class: 114
      Script: {instanceID: 30952}
    - Class: 114
      Script: {instanceID: 31002}
    - Class: 114
      Script: {instanceID: 31096}
    - Class: 114
      Script: {instanceID: 31108}
    - Class: 114
      Script: {instanceID: 31166}
    - Class: 114
      Script: {instanceID: 31270}
    - Class: 114
      Script: {instanceID: 31312}
    - Class: 114
      Script: {instanceID: 31378}
    - Class: 114
      Script: {instanceID: 31424}
    - Class: 114
      Script: {instanceID: 31524}
    - Class: 114
      Script: {instanceID: 31618}
    - Class: 114
      Script: {instanceID: 31638}
    - Class: 114
      Script: {instanceID: 31650}
    - Class: 114
      Script: {instanceID: 31678}
    - Class: 114
      Script: {instanceID: 31708}
    - Class: 114
      Script: {instanceID: 31778}
    - Class: 114
      Script: {instanceID: 31782}
    - Class: 114
      Script: {instanceID: 31786}
    - Class: 114
      Script: {instanceID: 31788}
    - Class: 114
      Script: {instanceID: 31834}
    - Class: 114
      Script: {instanceID: 31896}
    - Class: 114
      Script: {instanceID: 31910}
    - Class: 114
      Script: {instanceID: 31912}
    - Class: 114
      Script: {instanceID: 31964}
    - Class: 114
      Script: {instanceID: 32054}
    - Class: 114
      Script: {instanceID: 32068}
    - Class: 114
      Script: {instanceID: 32088}
    - Class: 114
      Script: {instanceID: 32116}
    - Class: 114
      Script: {instanceID: 32250}
    - Class: 114
      Script: {instanceID: 32260}
    - Class: 114
      Script: {instanceID: 32266}
    - Class: 114
      Script: {instanceID: 32276}
    - Class: 114
      Script: {instanceID: 32496}
    - Class: 114
      Script: {instanceID: 32510}
    - Class: 114
      Script: {instanceID: 32526}
    - Class: 114
      Script: {instanceID: 32542}
    - Class: 114
      Script: {instanceID: 32608}
    - Class: 114
      Script: {instanceID: 32634}
    - Class: 114
      Script: {instanceID: 32682}
    - Class: 114
      Script: {instanceID: 32734}
    - Class: 114
      Script: {instanceID: 32754}
    - Class: 114
      Script: {instanceID: 32966}
    - Class: 114
      Script: {instanceID: 32974}
    - Class: 114
      Script: {instanceID: 33066}
    - Class: 114
      Script: {instanceID: 33082}
    - Class: 114
      Script: {instanceID: 33090}
    - Class: 114
      Script: {instanceID: 33098}
    - Class: 114
      Script: {instanceID: 33136}
    - Class: 114
      Script: {instanceID: 33162}
    - Class: 114
      Script: {instanceID: 33226}
    - Class: 114
      Script: {instanceID: 33238}
    - Class: 114
      Script: {instanceID: 33304}
    - Class: 114
      Script: {instanceID: 33322}
    - Class: 114
      Script: {instanceID: 33402}
    - Class: 114
      Script: {instanceID: 33480}
    - Class: 114
      Script: {instanceID: 33528}
    - Class: 114
      Script: {instanceID: 33554}
    - Class: 114
      Script: {instanceID: 33626}
    - Class: 114
      Script: {instanceID: 33642}
    - Class: 114
      Script: {instanceID: 33650}
    - Class: 114
      Script: {instanceID: 33662}
    - Class: 114
      Script: {instanceID: 33674}
    - Class: 114
      Script: {instanceID: 33682}
    - Class: 114
      Script: {instanceID: 33736}
    - Class: 114
      Script: {instanceID: 33770}
    - Class: 114
      Script: {instanceID: 33796}
    - Class: 114
      Script: {instanceID: 33824}
    - Class: 114
      Script: {instanceID: 34012}
    - Class: 114
      Script: {instanceID: 34052}
    - Class: 114
      Script: {instanceID: 34054}
    - Class: 114
      Script: {instanceID: 34078}
    - Class: 114
      Script: {instanceID: 34224}
    - Class: 114
      Script: {instanceID: 34246}
    - Class: 114
      Script: {instanceID: 34256}
    - Class: 114
      Script: {instanceID: 34280}
    - Class: 114
      Script: {instanceID: 34306}
    - Class: 114
      Script: {instanceID: 34392}
    - Class: 114
      Script: {instanceID: 34666}
    - Class: 114
      Script: {instanceID: 34872}
    - Class: 114
      Script: {instanceID: 34882}
    - Class: 114
      Script: {instanceID: 34968}
    - Class: 114
      Script: {instanceID: 34976}
    - Class: 114
      Script: {instanceID: 34988}
    - Class: 114
      Script: {instanceID: 35002}
    - Class: 114
      Script: {instanceID: 35046}
    - Class: 114
      Script: {instanceID: 35222}
    - Class: 114
      Script: {instanceID: 35264}
    - Class: 114
      Script: {instanceID: 35382}
    - Class: 114
      Script: {instanceID: 35406}
    - Class: 114
      Script: {instanceID: 35676}
    - Class: 114
      Script: {instanceID: 35724}
    - Class: 114
      Script: {instanceID: 35764}
    - Class: 114
      Script: {instanceID: 35888}
    - Class: 114
      Script: {instanceID: 35930}
    - Class: 114
      Script: {instanceID: 35970}
    - Class: 114
      Script: {instanceID: 35994}
    - Class: 114
      Script: {instanceID: 36014}
    - Class: 114
      Script: {instanceID: 36112}
    - Class: 114
      Script: {instanceID: 36176}
    - Class: 114
      Script: {instanceID: 36184}
    - Class: 114
      Script: {instanceID: 36210}
    - Class: 114
      Script: {instanceID: 36236}
    - Class: 114
      Script: {instanceID: 36266}
    - Class: 114
      Script: {instanceID: 36330}
    - Class: 114
      Script: {instanceID: 36354}
    - Class: 114
      Script: {instanceID: 36456}
    - Class: 114
      Script: {instanceID: 36470}
    - Class: 114
      Script: {instanceID: 36490}
    - Class: 114
      Script: {instanceID: 36740}
    - Class: 114
      Script: {instanceID: 36976}
    - Class: 114
      Script: {instanceID: 36984}
    - Class: 114
      Script: {instanceID: 37094}
    - Class: 114
      Script: {instanceID: 37150}
    - Class: 114
      Script: {instanceID: 37174}
    - Class: 114
      Script: {instanceID: 37200}
    - Class: 114
      Script: {instanceID: 37290}
    - Class: 114
      Script: {instanceID: 37294}
    - Class: 114
      Script: {instanceID: 37316}
    - Class: 114
      Script: {instanceID: 37366}
    - Class: 114
      Script: {instanceID: 37384}
    - Class: 114
      Script: {instanceID: 37386}
    - Class: 114
      Script: {instanceID: 37562}
    - Class: 114
      Script: {instanceID: 37638}
    - Class: 114
      Script: {instanceID: 37718}
    - Class: 114
      Script: {instanceID: 37746}
    - Class: 114
      Script: {instanceID: 37824}
    - Class: 114
      Script: {instanceID: 37826}
    - Class: 114
      Script: {instanceID: 38206}
    - Class: 114
      Script: {instanceID: 38240}
    - Class: 114
      Script: {instanceID: 38392}
    - Class: 114
      Script: {instanceID: 38404}
    - Class: 114
      Script: {instanceID: 38662}
    - Class: 114
      Script: {instanceID: 38728}
    - Class: 114
      Script: {instanceID: 38854}
    - Class: 114
      Script: {instanceID: 38900}
    - Class: 114
      Script: {instanceID: 38952}
    - Class: 114
      Script: {instanceID: 38978}
    - Class: 114
      Script: {instanceID: 39008}
    - Class: 114
      Script: {instanceID: 39074}
    - Class: 114
      Script: {instanceID: 39086}
    - Class: 114
      Script: {instanceID: 39124}
    - Class: 114
      Script: {instanceID: 39136}
    - Class: 114
      Script: {instanceID: 39182}
    - Class: 114
      Script: {instanceID: 39230}
    - Class: 114
      Script: {instanceID: 39520}
    - Class: 114
      Script: {instanceID: 39566}
    - Class: 114
      Script: {instanceID: 39702}
    - Class: 114
      Script: {instanceID: 39760}
    - Class: 114
      Script: {instanceID: 39782}
    - Class: 114
      Script: {instanceID: 39818}
    - Class: 114
      Script: {instanceID: 39822}
    - Class: 114
      Script: {instanceID: 39858}
    - Class: 114
      Script: {instanceID: 39912}
    - Class: 114
      Script: {instanceID: 39962}
    - Class: 114
      Script: {instanceID: 40034}
    - Class: 114
      Script: {instanceID: 40142}
    - Class: 114
      Script: {instanceID: 40266}
    - Class: 114
      Script: {instanceID: 40316}
    - Class: 114
      Script: {instanceID: 40362}
    - Class: 114
      Script: {instanceID: 40410}
    - Class: 114
      Script: {instanceID: 40438}
    - Class: 114
      Script: {instanceID: 40450}
    - Class: 114
      Script: {instanceID: 40490}
    - Class: 114
      Script: {instanceID: 40526}
    - Class: 114
      Script: {instanceID: 40546}
    - Class: 114
      Script: {instanceID: 40586}
    - Class: 114
      Script: {instanceID: 40616}
    - Class: 114
      Script: {instanceID: 40782}
    - Class: 114
      Script: {instanceID: 40858}
    - Class: 114
      Script: {instanceID: 40940}
    - Class: 114
      Script: {instanceID: 40950}
    - Class: 114
      Script: {instanceID: 41330}
    - Class: 114
      Script: {instanceID: 41348}
    - Class: 114
      Script: {instanceID: 41410}
    - Class: 114
      Script: {instanceID: 41448}
    - Class: 114
      Script: {instanceID: 41522}
    - Class: 114
      Script: {instanceID: 41528}
    - Class: 114
      Script: {instanceID: 41542}
    - Class: 114
      Script: {instanceID: 41606}
    - Class: 114
      Script: {instanceID: 41632}
    - Class: 114
      Script: {instanceID: 41656}
    - Class: 114
      Script: {instanceID: 41768}
    - Class: 114
      Script: {instanceID: 41856}
    - Class: 114
      Script: {instanceID: 41880}
    - Class: 114
      Script: {instanceID: 42042}
    - Class: 114
      Script: {instanceID: 42074}
    - Class: 114
      Script: {instanceID: 42294}
    - Class: 114
      Script: {instanceID: 42322}
    - Class: 114
      Script: {instanceID: 42384}
    - Class: 114
      Script: {instanceID: 42392}
    - Class: 114
      Script: {instanceID: 42440}
    - Class: 114
      Script: {instanceID: 42452}
    - Class: 114
      Script: {instanceID: 42468}
    - Class: 114
      Script: {instanceID: 42490}
    - Class: 114
      Script: {instanceID: 42494}
    - Class: 114
      Script: {instanceID: 42530}
    - Class: 114
      Script: {instanceID: 42540}
    - Class: 114
      Script: {instanceID: 42614}
    - Class: 114
      Script: {instanceID: 42628}
    - Class: 114
      Script: {instanceID: 42704}
    - Class: 114
      Script: {instanceID: 42710}
    - Class: 114
      Script: {instanceID: 42750}
    - Class: 114
      Script: {instanceID: 42806}
    - Class: 114
      Script: {instanceID: 42856}
    - Class: 114
      Script: {instanceID: 42868}
    - Class: 114
      Script: {instanceID: 42914}
    - Class: 114
      Script: {instanceID: 42922}
    - Class: 114
      Script: {instanceID: 43040}
    - Class: 114
      Script: {instanceID: 43146}
    - Class: 114
      Script: {instanceID: 43154}
    - Class: 114
      Script: {instanceID: 43160}
    - Class: 114
      Script: {instanceID: 43186}
    - Class: 114
      Script: {instanceID: 43308}
    - Class: 114
      Script: {instanceID: 43422}
    - Class: 114
      Script: {instanceID: 681002}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 121
      Script: {instanceID: 0}
    - Class: 123
      Script: {instanceID: 0}
    - Class: 124
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 134
      Script: {instanceID: 0}
    - Class: 135
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 144
      Script: {instanceID: 0}
    - Class: 146
      Script: {instanceID: 0}
    - Class: 153
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 205
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    - Class: 241
      Script: {instanceID: 0}
    - Class: 243
      Script: {instanceID: 0}
    - Class: 245
      Script: {instanceID: 0}
    - Class: 258
      Script: {instanceID: 0}
    - Class: 320
      Script: {instanceID: 0}
    - Class: 329
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: 9d33f11375e9228e0534d1231798e4a7
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: MissileScript
  - hash:
      serializedVersion: 2
      Hash: b2d2a01a2a85d9ed12e80cb33a9a6044
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 233ed1311ce3ca16ffe4ea2b1ac14a93
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_DecalManager
  - hash:
      serializedVersion: 2
      Hash: f2353718d9b5d84992d5040bfc0012fa
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: truckrcccam
  - hash:
      serializedVersion: 2
      Hash: 445ce75260538b41ac4bffb621c0ffd7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_LevelLoader
  - hash:
      serializedVersion: 2
      Hash: ba14feed6f989d8a5350cdba5f9a5dd2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_MultipleBehaviorDemo
  - hash:
      serializedVersion: 2
      Hash: c09a59d12f535f2a4c806801f2de230e
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineClearShot
  - hash:
      serializedVersion: 2
      Hash: cf62aad97f6c2dd5d482e2509c6c9528
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: a3f3255b91dc31ecb1947d624464699a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_InputManager
  - hash:
      serializedVersion: 2
      Hash: 853183e95ed51a4be55f5c8b4207a17c
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainSpeedMonitor
  - hash:
      serializedVersion: 2
      Hash: 2c583ad0ac85d03e8700dd05fdcb46cb
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: Variables
  - hash:
      serializedVersion: 2
      Hash: ed8e62f07ec878233f5493a335003493
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: d35d813297b02080403ea856c9025c07
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FixedJoystick
  - hash:
      serializedVersion: 2
      Hash: b72d9c057c0f9460abf04218f4cc4f0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerMessageBox
  - hash:
      serializedVersion: 2
      Hash: 7ac5853804542d018ab947d645cfca15
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Skidmarks
  - hash:
      serializedVersion: 2
      Hash: 917a94b859e3d90e8709542ac4e1f3ed
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CrashShredder
  - hash:
      serializedVersion: 2
      Hash: 58894da276db6c2e7fad9a117ff2ba92
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: HonkZone_v3
  - hash:
      serializedVersion: 2
      Hash: e061bdd0159a784b8acc0552dd0f40bb
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 9ebc0a3016506a0cbb7e0306dfcb9497
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: DictionaryAsset
  - hash:
      serializedVersion: 2
      Hash: e7e95903ca66d58e1a7cbd71f824e16d
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 80a72b656b55a7a1fc92e1a02151ea22
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: ScreenSpaceReflections
  - hash:
      serializedVersion: 2
      Hash: 0370b9f95798139b666659c7e1be6147
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: eeb60086e1b12a60bc5a92d50ff8e58a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CarSelectionExample
  - hash:
      serializedVersion: 2
      Hash: d4926dd08d79f5c86d82bb1c90028189
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SMR_GeneratedMesh
  - hash:
      serializedVersion: 2
      Hash: 0c0b64d103fe555d830e8ca206abd12a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: fd7848ab165d107b83477df2672b764e
    assemblyName: Assembly-CSharp
    namespaceName: Kino
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: 0f9590a9f6c2cc62c14e7eb534dc219e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RearLookAt
  - hash:
      serializedVersion: 2
      Hash: febcfbf0a0d85e37808a3740e3c2c6fa
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: 4392bfff79efe0630144f16ea4d656ab
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectList
  - hash:
      serializedVersion: 2
      Hash: daeac1347f8039f61cd4017ff33bd3c6
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 8b15e35eb98fc258ec2e839df7c3c9b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSliderValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8bdf39615375786f137bc815d5b6454d
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineCollider
  - hash:
      serializedVersion: 2
      Hash: 0e51ca47100ed50f4c9914b672e6d6a1
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainAttachPassenger
  - hash:
      serializedVersion: 2
      Hash: 682dfc653b22feea53b38adb3bc66414
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: bfa9d6dfbc3c74a843a3b197bdcc6639
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: 0307d4f8e26c7dac23fdcc76db510661
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_SceneManager
  - hash:
      serializedVersion: 2
      Hash: fb07a4615cd7d2ccd54f63c52459f81f
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: BoatController
  - hash:
      serializedVersion: 2
      Hash: db9b308769d19e1f9e161df0392c23ca
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformChildrenChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: be3095732075cc6197d96e39e27715a5
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer
  - hash:
      serializedVersion: 2
      Hash: 9dd54ce33440072d6692a67e0c384ad1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: ea8afa1d8a27aa543637446521ed121f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_ChangableWheels
  - hash:
      serializedVersion: 2
      Hash: 817d719114f1c2641836415a0e0e772a
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineOrbitalTransposer
  - hash:
      serializedVersion: 2
      Hash: 4b921dbdbd3bf206a01cfcaac0ff5702
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineConfiner2D
  - hash:
      serializedVersion: 2
      Hash: f669be52680c88846b2093fd41d1e06e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 20a120e55ad3ab65556328d5fd8309dd
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: 2deddcdd5f664164bb803747210cbba2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: 2c222ebb75b0ef35fe7be3f252a988e3
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainCarCoupler
  - hash:
      serializedVersion: 2
      Hash: 8e955311c13f279362d381919f74fe9a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Light
  - hash:
      serializedVersion: 2
      Hash: 2bbd05175d4cdd21e24f9716cdd24f83
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: a124c24e6cc9deddf50c3c6a12280dfa
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Siren
  - hash:
      serializedVersion: 2
      Hash: 1362033cd491d66ad3cc206af29cc0de
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: da281dc879c7932e0c9669da88ae40c0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: 9507f221948bbd40853cc0ec75374d07
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareComponentSRP
  - hash:
      serializedVersion: 2
      Hash: f8cd26c2cacf5f94ee8d5b291e6cb5e7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_TruckTrailer
  - hash:
      serializedVersion: 2
      Hash: 2f8166e9dfb235c99d1f87b57ce59ca3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_WheelCollider
  - hash:
      serializedVersion: 2
      Hash: 4b6627b2ea8f2830039e3ef9d94829c8
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineShotQualityEvaluator
  - hash:
      serializedVersion: 2
      Hash: 803715a474631c015d973a2e8b1d4ebe
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_ShowroomCameraDrag
  - hash:
      serializedVersion: 2
      Hash: c77a06db4d15575553e8821199e3f93e
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineGroupComposer
  - hash:
      serializedVersion: 2
      Hash: cda376115e17be97d3e17acbc9cbcb6e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Waypoint
  - hash:
      serializedVersion: 2
      Hash: 58ba87a75cba3a2d02f0d27c36bd4615
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customization_API
  - hash:
      serializedVersion: 2
      Hash: e6ee7dccba41ae87d10e3d922c6cb71c
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: 522f6865b4ec12522e602206785309f8
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: GlobalMessageListener
  - hash:
      serializedVersion: 2
      Hash: 53e329840e6d03aebec76623c3b054db
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 4ea1bf258a208f00f70cf744bd7e56ce
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: 372f134920c8988b3fb7f35d325236fc
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WheelManager
  - hash:
      serializedVersion: 2
      Hash: d55fe87545e59658bfde174cbd920a61
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: WaterEffect
  - hash:
      serializedVersion: 2
      Hash: ef19274111491b6b8bcc6fd227f656f5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: ecb82fa069f447e07427a2f3b70a7ccd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GunShoot
  - hash:
      serializedVersion: 2
      Hash: ee34255e3e86289bde9bad5e07b84cbc
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollRectValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: f4bf259fb218f3798acea5fff1a19934
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Demo
  - hash:
      serializedVersion: 2
      Hash: dd9dda1dfd39e605c49e72003342ef5d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: e574679fa685b5f5b457cbc01add2246
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Drivevehicleenterexit
  - hash:
      serializedVersion: 2
      Hash: 41f662358bf189c0f48512c2e3f57b20
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: BulletScript
  - hash:
      serializedVersion: 2
      Hash: 0126f347d98d02424c7e030465a71ead
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: f379904be705ab5dd721d6f3b4bdf3d2
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineRotateWithFollowTarget
  - hash:
      serializedVersion: 2
      Hash: 7f26049599d9a4d8fd9d80bf92396965
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: RadarItem
  - hash:
      serializedVersion: 2
      Hash: ce6d4059b010f099c83a496e477c3bc0
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_Demo_Translate
  - hash:
      serializedVersion: 2
      Hash: afcebc561f7e61a69c75a8beadce605e
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_ParticleText
  - hash:
      serializedVersion: 2
      Hash: 66496b917404bcb20f6bf50861c1adc6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumeBakingSet
  - hash:
      serializedVersion: 2
      Hash: dbe4f05d59170430398f6453d7253fe5
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: 6d971045f8e67def7430d0b6f3cb0700
    assemblyName: Unity.RenderPipelines.GPUDriven.Runtime
    namespaceName: UnityEngine.Rendering
    className: DisallowSmallMeshCulling
  - hash:
      serializedVersion: 2
      Hash: 7bdff9e91edfd9600f92a185def7e34a
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptMachine
  - hash:
      serializedVersion: 2
      Hash: 0a3f328f3ea906bbbcf794f4c27ee59d
    assemblyName: Unity.VisualScripting.Flow
    namespaceName: Unity.VisualScripting
    className: ScriptGraphAsset
  - hash:
      serializedVersion: 2
      Hash: 3f47b6f142a0d8b4ac91edce23a52c9c
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: LinkedSplineFollower
  - hash:
      serializedVersion: 2
      Hash: ff0dd8fd174d68c3922f7d6edb1aba73
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: FollowTargetCamera
  - hash:
      serializedVersion: 2
      Hash: 33f160a5c07e580c29aba2bd1e4db811
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: c65fb8f721cc4462e7a642974c37d3c0
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: b28819ff410f1bab0c0a31de5979705a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CustomizationSetups
  - hash:
      serializedVersion: 2
      Hash: de8e34fffe62b77ca47bae273d0414ba
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ObsoleteProbeVolumeAsset
  - hash:
      serializedVersion: 2
      Hash: 85378b07084a5c9d29174d50142e3c1a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_SuspensionArm
  - hash:
      serializedVersion: 2
      Hash: 2cc6483081c8f582b56ab72996bc9b2c
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: GameCanvas
  - hash:
      serializedVersion: 2
      Hash: 318c05e600784246d034e8495772110e
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineConfiner3D
  - hash:
      serializedVersion: 2
      Hash: ccd4c979962b9526251a18a3d420c183
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Settings
  - hash:
      serializedVersion: 2
      Hash: 948f742c14cf06ad1505535fa059fff9
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineCamera
  - hash:
      serializedVersion: 2
      Hash: c4cdb902228df56d5b2b639d6a6bbd3c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: 5baec78bce2e306d25f336cdc88245a2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_PoliceSiren
  - hash:
      serializedVersion: 2
      Hash: c00826b2379e4bc3f3b4fb846b0a162d
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineBlenderSettings
  - hash:
      serializedVersion: 2
      Hash: 1bc6ff8d02d2d68019aa6a028f8d409d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: e987953190eb4c12cf19df790566dc2c
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: d6e44b78733a0cf980e5564336acd559
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: BulletScript
  - hash:
      serializedVersion: 2
      Hash: 0bc7ef1b7516f21320ec49bfd31eef2e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: 33656f83bb0079184b2206a69690ec46
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: da5a949a0bbd2ff91bc51c5805c2c41f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpAsButtonMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9104447b683bf70406fa01f12ecb564a
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: ea5e0cb2f62c5bafc3dfb7bcdc9595e1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: a92561298b80715aa69e8fa770123cb5
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: d3819828766fb7113c2eb87537362385
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_SirenManager
  - hash:
      serializedVersion: 2
      Hash: b14e186823458f549289789cccbb77ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: 517520a0f77900f65bb128b1434c57e0
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: GameCanvas
  - hash:
      serializedVersion: 2
      Hash: 491f8b114486926653b46580bbf97646
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_FuelStation
  - hash:
      serializedVersion: 2
      Hash: 722208307ebb0d8f6bec35863c021674
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineTrackedDolly
  - hash:
      serializedVersion: 2
      Hash: 67272c9c30c50b68ef1ce72ac0191c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMoveMessageListener
  - hash:
      serializedVersion: 2
      Hash: 62b4b73b93fe0ff7a3574e335adbc2f3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Engine
  - hash:
      serializedVersion: 2
      Hash: 33399aa50372dcde640a228b0ff54e2d
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineThirdPersonFollow
  - hash:
      serializedVersion: 2
      Hash: b227e099fa1bb4b51a2d3fc5fdf1737f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Canvas_Customizer
  - hash:
      serializedVersion: 2
      Hash: 6b2b20d5352b9f1294595326f54bd3c2
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObjectPopupField
  - hash:
      serializedVersion: 2
      Hash: 191c3402cd43ee08f0a96795440c294e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: 0e77548cd8882e0089a18a0a0dc54230
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Speed
  - hash:
      serializedVersion: 2
      Hash: d6021231d04d6dccc756e3765b003c89
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.TerrainTools
    className: WSM_TerrainBackup
  - hash:
      serializedVersion: 2
      Hash: 915204dac8acad99f076d6e72ada0a0f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: c2e90e282e5668c3719d770553a63bce
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: SwitchTrigger
  - hash:
      serializedVersion: 2
      Hash: 95426d463074947dcbffc797c44e779c
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: StandaloneInputModuleModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 8945228a6907175082722efcd681689f
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainSpawner
  - hash:
      serializedVersion: 2
      Hash: 341596dcc2c2084304e9dd8d02b4a372
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Wheel
  - hash:
      serializedVersion: 2
      Hash: 61d518880a200cef9e623b15dfc18775
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Skidmarks
  - hash:
      serializedVersion: 2
      Hash: f1504b36ea8dbb329123bfed7268467b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DynamicJoystick
  - hash:
      serializedVersion: 2
      Hash: 0959cf9ac93be7b2125ff42cb8efc9d0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Upgrade
  - hash:
      serializedVersion: 2
      Hash: c0a2b4d13675f08bfb211e044c334e68
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: c77b8049ee2d49480abd1cf884014792
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineImpulseSource
  - hash:
      serializedVersion: 2
      Hash: beb5e5ccf971dff77267f6d5d07e3095
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineImpulseListener
  - hash:
      serializedVersion: 2
      Hash: e63fe08659e7e4647a51098c666f8845
    assemblyName: Unity.InputSystem
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: aeabf1b9984c63aa96281fb83b3bdfbd
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: NoiseSettings
  - hash:
      serializedVersion: 2
      Hash: afb7b65d28d5a7423e69a1b076be0064
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 4728e3d21cc2a12e391e71ec9b7eed0b
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 39f9e2466c2f4ec5446f059f06d600ea
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainDoorsController
  - hash:
      serializedVersion: 2
      Hash: b30c8635462e66ac20f4241106119f77
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateMachine
  - hash:
      serializedVersion: 2
      Hash: 15bdeb9669a4fe5f738fed9441046056
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineRecomposer
  - hash:
      serializedVersion: 2
      Hash: 68a709361b20592bc842a09aefcbab2c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Prop
  - hash:
      serializedVersion: 2
      Hash: 89ca47cea2a1f8f83a1bac574ca8e424
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GunAim
  - hash:
      serializedVersion: 2
      Hash: cf7c50285ca396e59d789ff862e066df
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_SkidmarksManager
  - hash:
      serializedVersion: 2
      Hash: 3b5263a98bcc71d9a9d1d78a31b34ced
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 2438092ea770d64a143f4ef66dcc8c4e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_MobileButtons
  - hash:
      serializedVersion: 2
      Hash: d41ab9b5ea92474f9c0a6937a0607ce1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: 110aa327d5a5138dcfcbacb388ae0acc
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessEffectSettings
  - hash:
      serializedVersion: 2
      Hash: f040a0c4879eb5d82a4e17a2f9106218
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Paint
  - hash:
      serializedVersion: 2
      Hash: 0f6b6107168e347e854c0107aa8cb9fb
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: f5425df87256ee08584eaf48afc35f26
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Exhaust
  - hash:
      serializedVersion: 2
      Hash: ad6438965fe7141d1e79e2824b1e92d9
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Decal
  - hash:
      serializedVersion: 2
      Hash: 51b80d3734ea86b38e3101db98029e15
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: df058709c8f760e553df11da23d79e21
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_WheelManager
  - hash:
      serializedVersion: 2
      Hash: ab451d9cd6e084eda12896e95f785737
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Joystick
  - hash:
      serializedVersion: 2
      Hash: 7d026ae582dc02c5edc3fc23b8c03ff7
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: b72839eb6e4350778dc879d98832aa2b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: 67ba98f8a8977f3e60e5f0e049923033
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: 7bb32d6e4c7614a97d9723ce7e26588c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: BasicPlayableBehaviour
  - hash:
      serializedVersion: 2
      Hash: 501898370ee0904e6a113faaed11fffd
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineTransposer
  - hash:
      serializedVersion: 2
      Hash: d0cdd713f9bf4e6a625d9e7804deca42
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 4367594cef8038e7a571179cbb9e5897
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSplineDolly
  - hash:
      serializedVersion: 2
      Hash: 2b2b94d61495162bc96537dd56f87ab8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_TrailerAttachPoint
  - hash:
      serializedVersion: 2
      Hash: abadd05c6317e5e0f8ed2c103b78c218
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: CollactableScript
  - hash:
      serializedVersion: 2
      Hash: 23e4ea77dbfb5c8462b4a20572212dfb
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: SplineBasedTrainCarCoupler
  - hash:
      serializedVersion: 2
      Hash: 74fd5ed9e9f88d13242327b2b03b8c9e
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainWheelsTruck
  - hash:
      serializedVersion: 2
      Hash: e88d71ef93aba4e7faf243430f6a10ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: MacroScriptableObject
  - hash:
      serializedVersion: 2
      Hash: 84d498be29416a4a99bcdd3c2b236c83
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Caliper
  - hash:
      serializedVersion: 2
      Hash: 7c18a524d50a8ff802ee18b3350f314a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Teleporter
  - hash:
      serializedVersion: 2
      Hash: d1f8c7ed152700835aef5cb4da7e1ebe
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: Spline
  - hash:
      serializedVersion: 2
      Hash: bcef459fdf6dbd7cda947237b100c737
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: WaterSplashScript
  - hash:
      serializedVersion: 2
      Hash: 4f353524ce6564e40764f1ea080b2d85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: b4f05a593336f724236cdb4fdb4d61cd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ParticleCollision
  - hash:
      serializedVersion: 2
      Hash: 13f3cf24e0245c69bcaa85f14201eaa3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: FloatingJoystick
  - hash:
      serializedVersion: 2
      Hash: 096697c69786e4e7a688a8ed5bec008e
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineStoryboard
  - hash:
      serializedVersion: 2
      Hash: 02a47340661d2ea7c6d30e292cfef403
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnToggleValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8a9870e5909a2d5fbe6c3fc783653bd0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_DetachablePart
  - hash:
      serializedVersion: 2
      Hash: a3901985eb076c1c9b7562fca82ca3e0
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: d7e96237847e9b7ebf698e6a85de5099
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineFollow
  - hash:
      serializedVersion: 2
      Hash: 1ba51ee5cf0731c12b46b7142f25c8a9
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: BoatEngineSound
  - hash:
      serializedVersion: 2
      Hash: f295412bc00b58b9095a71e9764cc886
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: 4749e7073cc4b5627afa305de577b316
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_NeonManager
  - hash:
      serializedVersion: 2
      Hash: 8e6dc3d79712ca4934f6d50c80f010b1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: f5d807f7ddc676102296c6245a419cbc
    assemblyName: Assembly-CSharp
    namespaceName: Invector.Utils
    className: vComment
  - hash:
      serializedVersion: 2
      Hash: 035633c90ee8d8cf9a02e04922a59238
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSmoothPath
  - hash:
      serializedVersion: 2
      Hash: c6369b4cc3be620d6c9300e920bc3d52
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesAsset
  - hash:
      serializedVersion: 2
      Hash: cf32d98954e63b8e30e9334b152e9b8e
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 68425c0dcca473e637c35e47c549519c
    assemblyName: Assembly-CSharp
    namespaceName: Invector.vCharacterController
    className: vThirdPersonAnimator
  - hash:
      serializedVersion: 2
      Hash: a5a2404c0b8cf518b73c8156fbc91dfc
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineExternalImpulseListener
  - hash:
      serializedVersion: 2
      Hash: df9aee1894d42581df414c1e29a80b2a
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineRotationComposer
  - hash:
      serializedVersion: 2
      Hash: 08e2446b65d2da7600c6bbdd36de0e56
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 7b28cfd5f574253b8cf8ac52b199b4b3
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SplinePrefabSpawner
  - hash:
      serializedVersion: 2
      Hash: f41afcb9a4025cf91d39ebcd0074d37a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ParticleMenu
  - hash:
      serializedVersion: 2
      Hash: b24bae4511d404331640e62f8b8c2ed0
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumesOptions
  - hash:
      serializedVersion: 2
      Hash: c87a29ed5cc1bfd6a554725719ee4df7
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSplineRoll
  - hash:
      serializedVersion: 2
      Hash: 681cbc6a19eb22f9f164d8c83acdc61d
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineFollowZoom
  - hash:
      serializedVersion: 2
      Hash: 48c5884d4bc0440f75eb0f2a792ff2f7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_SpeedLimiter
  - hash:
      serializedVersion: 2
      Hash: 8dff8e9853297d341afa0c11f9091b59
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 1321993db8249e1e05e1e0492b2bbdc3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameVisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 5289cc8018a9b4b277e790bb6131262b
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineMixingCamera
  - hash:
      serializedVersion: 2
      Hash: d90629714fa6b0145fcaffc87f5c6559
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b6307dfc54cca9ba44f47185c578b0ce
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9c559db8589e96c92a318975f10409b8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: MobileUIController
  - hash:
      serializedVersion: 2
      Hash: fe0a072dbdcbca34d5003c5299de3aaf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: 8dc40a01667ca02b3354a4e7a61be082
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: f876322b9578216c9606ab410de66132
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Spoiler
  - hash:
      serializedVersion: 2
      Hash: 4706fdc117f1da51f25c17cf3121b432
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_CustomizationSlider
  - hash:
      serializedVersion: 2
      Hash: bf0842d88681628b29beb00b5e2ab785
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDeselectMessageListener
  - hash:
      serializedVersion: 2
      Hash: ad0d127b487013ca6102381ba7099109
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_RepairStation
  - hash:
      serializedVersion: 2
      Hash: 1386e0b402ffb3fdbfda5b77ea0326f1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: PoliceLights
  - hash:
      serializedVersion: 2
      Hash: bc75926bfd3609757f7bf33ff766f026
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: acdd4a858cdfa1141c6d5cb64cae45d7
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainDoor
  - hash:
      serializedVersion: 2
      Hash: 33eb495d99aa9bea4dbe8b4c4e02c7bb
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 4878e735441f4041539853759559c86f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 439c170205f7479568d8d95fddf343cd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_SteeringWheelController
  - hash:
      serializedVersion: 2
      Hash: e0cf060d3a0d1afae0c661ac42606ab8
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolume
  - hash:
      serializedVersion: 2
      Hash: 40be2ad159aa1a2b72ecc74cb38c7824
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 9b91f49e9296cf4540b949fe8dcc7528
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: CameraManager
  - hash:
      serializedVersion: 2
      Hash: 6e0b9b9d86a552b1cac148cddfa46fb4
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: RailroadSwitch_v3
  - hash:
      serializedVersion: 2
      Hash: ce8ae01f671325d3ef35aa28b00446c6
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_BehaviorTester
  - hash:
      serializedVersion: 2
      Hash: 715f7928b39881049ce8c46350cb8681
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTransformParentChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: add2faef925880bdee63dc01d10f2341
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_DashboardButton
  - hash:
      serializedVersion: 2
      Hash: 430cf7b8fa44396ac4030a652454861d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_UpgradeManager
  - hash:
      serializedVersion: 2
      Hash: 6cf63e9dc888f92a3672d2e4db631c8e
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: 3074afe1b03a0fb081e176a4ef1b9d09
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: 2d866cfe6a23eaa57e27aafc8dfba888
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: HeliRotorController
  - hash:
      serializedVersion: 2
      Hash: 0860e6a7a2fbcc0a57f8ef16016f848a
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineFixedSignal
  - hash:
      serializedVersion: 2
      Hash: 02fd513ed3f132966485e69c4a77124d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_DecalSetLocation
  - hash:
      serializedVersion: 2
      Hash: fa8261e9471b6408b9f20a5870306152
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: ReverseDirectionZone
  - hash:
      serializedVersion: 2
      Hash: 72707c171e9b916ce66cda483cba3bb6
    assemblyName: Assembly-CSharp
    namespaceName: Invector.vCharacterController
    className: vThirdPersonMotor
  - hash:
      serializedVersion: 2
      Hash: 113e54f67456d0e7754c0598ee2a6dd4
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeVolumePerSceneData
  - hash:
      serializedVersion: 2
      Hash: 28fd3d045acc780719a17f02073eb448
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 5a9c960fe06e2673290669d21aa0d69c
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePixelPerfect
  - hash:
      serializedVersion: 2
      Hash: 6e778c2762d85debab2a8d56193feb86
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineOrbitalFollow
  - hash:
      serializedVersion: 2
      Hash: e49f64f6c7006f2a8a0e7674b5de1fae
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 112f1b782c8054fe0791bd8774367be2
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineTouchInputMapper
  - hash:
      serializedVersion: 2
      Hash: c4036fafc16f59c024cd4b9758917f32
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineInputAxisController
  - hash:
      serializedVersion: 2
      Hash: d5c3d705ef2ea1fd2dcfd2afa1859ace
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: vPickupItem
  - hash:
      serializedVersion: 2
      Hash: 90cf308600fb4aeb677786843453ec55
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: c833d8ddc320fe5153f6a88cef23c858
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerObject
  - hash:
      serializedVersion: 2
      Hash: 47f78e94e3df8d612cefa1307a19e2ce
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: b7dbc8a0b64e915010ba171c34bf05ff
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Spawner
  - hash:
      serializedVersion: 2
      Hash: d3dfdcc705a790b50c5adebf91dbaf57
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_ParticleTextFontAsset
  - hash:
      serializedVersion: 2
      Hash: 46ab850114763848898a5db43ccca3a4
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: RadarSystem
  - hash:
      serializedVersion: 2
      Hash: 4fcf0bb4aff1d14a1dfcdedc4b07bd05
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 8d7ab1303b1c14da5fdd62bf96ca6e89
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: 424074d3010ce4ec3d155fbdba52a3b3
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: Grain
  - hash:
      serializedVersion: 2
      Hash: 8703b6ae2b0eefd9dafbc44d8d333fb1
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: 1ab136900c0edaeeb49424c852595030
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 06785fe79abab1af72fd807b67d2c2cf
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Drag
  - hash:
      serializedVersion: 2
      Hash: 201a2de2c83b6b26812c1ebb6bc5461e
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: Gasoline
  - hash:
      serializedVersion: 2
      Hash: 5015c05a2113f320567617675a1ebe33
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 7053e305cdc1eb560f4c3be49d3b5136
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: e5f9868e485ac5248f96a3be584a5e4a
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 810e3c228b295cc60c4de2cdd94b18cb
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 84b1cd587ef07a8ccd0cf0f3aecd5cf3
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: SplineBasedWagon
  - hash:
      serializedVersion: 2
      Hash: 9e38ca037c6c29f17bf769ee97b38595
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SplineFollower
  - hash:
      serializedVersion: 2
      Hash: f2098fcd8ee983a05ec192f63904298f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnEndDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: a05ebab0135906d784909473ba650bee
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_DashboardInputs
  - hash:
      serializedVersion: 2
      Hash: 953fad5c5ab1e7bb889420c434db93fe
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: e81bf7d4290eb9291aa9e23e90b97774
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineBrain
  - hash:
      serializedVersion: 2
      Hash: d68d5e4a9f3f4ffdc3515d03eea6f2b1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_HoodCamera
  - hash:
      serializedVersion: 2
      Hash: a908b098f2284663c0b9130e88844772
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: c4b85a67513b4f0cf7b5fb2d5ded04b0
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 2fd42819c69acefd80a2bbcd49cbe8cf
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessProfile
  - hash:
      serializedVersion: 2
      Hash: 429ce0b58f9d2275bbc4ed740bee78ec
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessDebug
  - hash:
      serializedVersion: 2
      Hash: 67ef69ed34f87102f53e965c59b5ee1b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_DashboardDisplay
  - hash:
      serializedVersion: 2
      Hash: c152307c7934a040fa20d52a886f8e77
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSplineSmoother
  - hash:
      serializedVersion: 2
      Hash: 711ba963e2b245a25de71beeaeeeb658
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 3b6ada30a4da43b504f82391645d42a7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_OverrideInputsExample
  - hash:
      serializedVersion: 2
      Hash: 0a71303844099e1a07d3331719c799d7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_GroundMaterials
  - hash:
      serializedVersion: 2
      Hash: 9f355e210e7950e47ad1391c7681435b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 43ebd945b04bced376c791411cf5a289
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSelectMessageListener
  - hash:
      serializedVersion: 2
      Hash: 224cd18ad901bd0b2e58fc0c50025388
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: BoatSystemManager
  - hash:
      serializedVersion: 2
      Hash: 1ddccc9c3a58e54eb8e26c06445acef1
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineFramingTransposer
  - hash:
      serializedVersion: 2
      Hash: ba21b952ad0710e5eaed076e3e89df38
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AeroplaneEngine
  - hash:
      serializedVersion: 2
      Hash: 3627438fae6dba806ab6b980e10a2255
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSplineCart
  - hash:
      serializedVersion: 2
      Hash: a9f0153fc675e1f4ade818b83e4ff0c1
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 13f262cc0741566a7ab72682984bc3e1
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: be42f910347a33f4b2c9f6ca4e4bfc93
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_ShowroomCamera
  - hash:
      serializedVersion: 2
      Hash: c27a8d9749b8e0f0b4c594f264548ac6
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineBrainEvents
  - hash:
      serializedVersion: 2
      Hash: ad28bb2ac405fd01da6ea8b92684e4f2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: vThirdPersonCamera
  - hash:
      serializedVersion: 2
      Hash: a19216c51dea4b9312068ef82662f619
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineCameraEvents
  - hash:
      serializedVersion: 2
      Hash: 0f5a086d4d028363983ba6ca30a01397
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseOverMessageListener
  - hash:
      serializedVersion: 2
      Hash: b235c23342f460997138502ac21fd7d8
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineBasicMultiChannelPerlin
  - hash:
      serializedVersion: 2
      Hash: aca462ce2ffac1914198dec5248456ba
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: EnemyAI
  - hash:
      serializedVersion: 2
      Hash: e23d2210701e34e6850023cabe554c1f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnInputFieldEndEditMessageListener
  - hash:
      serializedVersion: 2
      Hash: 89f5267a051485e2d09be2841bc7b5ed
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: UIButtonConnector
  - hash:
      serializedVersion: 2
      Hash: beb25a432387bef8c88c05bc21a515a4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_AIBrakeZonesContainer
  - hash:
      serializedVersion: 2
      Hash: 96de712da86e617d0f8cf9e8225fba94
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BikeSwitch
  - hash:
      serializedVersion: 2
      Hash: e0b73dd6dc250e5794c2e78daea1162d
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainProfile
  - hash:
      serializedVersion: 2
      Hash: 099fc41b7c72e5944ec297f967f22b1b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BikeAnimation
  - hash:
      serializedVersion: 2
      Hash: 17790149a71a7fabcce72547ffa385bf
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Brake
  - hash:
      serializedVersion: 2
      Hash: 3773805f0a1ff0ade20d5353c2a21878
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineDoNotUpgrade
  - hash:
      serializedVersion: 2
      Hash: 29f8ad1cfbed6aa584c6c736400d5232
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePostProcessing
  - hash:
      serializedVersion: 2
      Hash: ee8cd2be783205a7c712839d5deb3b3c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: JoystickSetterExample
  - hash:
      serializedVersion: 2
      Hash: a90f6a00b23520788be7d89ee3b0773d
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 19fee9f0e578bc949f38ee1d1be2ceb8
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_Effect
  - hash:
      serializedVersion: 2
      Hash: 58b9d8000b5d14d172901e6a52eb900b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_SpoilerManager
  - hash:
      serializedVersion: 2
      Hash: a76021eccb37dfb355be04a04f5b6051
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_CustomizationManager
  - hash:
      serializedVersion: 2
      Hash: 43da40deb245b47b1a669efa1b942acd
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineFreeLookModifier
  - hash:
      serializedVersion: 2
      Hash: 8f8118cbbc57e7ab94f40e42abd7e2b6
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineVirtualCamera
  - hash:
      serializedVersion: 2
      Hash: 987a2f42d4dfcdd35c82821a2f39743a
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSplineDollyLookAtTargets
  - hash:
      serializedVersion: 2
      Hash: 270fca4b837e37c25ae16abcf2391c77
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: StationStopTrigger
  - hash:
      serializedVersion: 2
      Hash: f5df081962c06a171f380eb236cd1f3c
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: 4a63b123b0ce6f9d95f7f10dbe429067
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Useless
  - hash:
      serializedVersion: 2
      Hash: a9c3b9a784fbe7c35ff2a48db4ee7745
    assemblyName: Assembly-CSharp
    namespaceName: Invector.vCharacterController
    className: vThirdPersonInput
  - hash:
      serializedVersion: 2
      Hash: 215d2dc6ec6ea06728398ea39a103cb3
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: 296bd0d84b42862d197386d5dbc46ad2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: allcanvasfalse
  - hash:
      serializedVersion: 2
      Hash: e8fd74e57e72e8e4ef28792881adb864
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: 58570e231c93c2f96918856759b81286
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: 0b02b391abe43ca8ac5647ef0c8bb992
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: UniqueMesh
  - hash:
      serializedVersion: 2
      Hash: e8d390bd9ecacc453a7500b90d0c0292
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: a89b0448af4e1cd103a77f9fec0a961f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnSubmitMessageListener
  - hash:
      serializedVersion: 2
      Hash: fb91b0f531b5adecb53c856ab1c2f0aa
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: b3941595ae005f7936e1a3c38fca54d8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CarControllerV4
  - hash:
      serializedVersion: 2
      Hash: 5a4aa2850fe11587f47e34c270c7fd22
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: 86344100c716167fb86137c127ca9eee
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: e6f8333cc8a800e2866870a5ef7efc35
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnControllerColliderHitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 68589f954d791c9cedae48d270204814
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineThirdPersonAim
  - hash:
      serializedVersion: 2
      Hash: 1c3b6e1cf2dd4ac0d64f1a784d622f6b
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainStationController
  - hash:
      serializedVersion: 2
      Hash: 4a49922a07ddcb523008d8c074c9d5f5
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: MissileScript
  - hash:
      serializedVersion: 2
      Hash: ********************************
    assemblyName: Unity.VisualScripting.State
    namespaceName: Unity.VisualScripting
    className: StateGraphAsset
  - hash:
      serializedVersion: 2
      Hash: ********************************
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_InfoLabel
  - hash:
      serializedVersion: 2
      Hash: b3866c0c6ac1f6905af7956c9e9c7cd2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AirplaneControl
  - hash:
      serializedVersion: 2
      Hash: 8ed5daadd237bb82e132790f75ffccaf
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: 4451001488dcdd6a74f9282551fdfc3e
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Cameras
    className: FlyingCamera
  - hash:
      serializedVersion: 2
      Hash: 17381ac6f63b1993b0882fbbe000f4ab
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineLegacyCameraEvents
  - hash:
      serializedVersion: 2
      Hash: 568292fb7ea6c4f7a667e5ff76bc7e85
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseUpMessageListener
  - hash:
      serializedVersion: 2
      Hash: 55060a7bf226ac385ce19ebd450f199f
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 97295426908a1cfa6c3b547faeff3a0c
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineFreeLook
  - hash:
      serializedVersion: 2
      Hash: 7e12302f66e3c8ed5bcfea4ce65fccea
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityMessageListener
  - hash:
      serializedVersion: 2
      Hash: 6d94138b60a9299798dda96c1ee6725f
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBeginDragMessageListener
  - hash:
      serializedVersion: 2
      Hash: 9da926302ede7010a2620f727037e201
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineConfiner
  - hash:
      serializedVersion: 2
      Hash: b3e0d3861a408e8a6b0d351ba989f8e7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: SMR_Theme
  - hash:
      serializedVersion: 2
      Hash: a0d7a039a57e8e6a6e6deff90996a984
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SMR_GeneratedCollider
  - hash:
      serializedVersion: 2
      Hash: 1553f209ae0b2d5d3a35685fca55f9c3
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 8bf800a764665b085fe469e8aea7f167
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 14b24b1bb880bc45a90a71ba7d132adb
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CustomizationTrigger
  - hash:
      serializedVersion: 2
      Hash: f854af586011dd194574f89546642e29
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8290ea1585c210a3b8a7cc48fd838299
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: GyroscopeViewController
  - hash:
      serializedVersion: 2
      Hash: 9a41f6fbdb23b1dc3461204ccec69629
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePanTilt
  - hash:
      serializedVersion: 2
      Hash: f3dea08bb9b0271aa1c5bf872d5ec635
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineAutoFocus
  - hash:
      serializedVersion: 2
      Hash: 704662b4c37c177b3f180b7b725ead3b
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: Cinemachine3rdPersonFollow
  - hash:
      serializedVersion: 2
      Hash: d4c7cb9e46d192844a5b295d6a3f8dc5
    assemblyName: Unity.RenderPipelines.GPUDriven.Runtime
    namespaceName: UnityEngine.Rendering
    className: DisallowGPUDrivenRendering
  - hash:
      serializedVersion: 2
      Hash: bf6b10d9be571b3e28072274709014d3
    assemblyName: Assembly-CSharp-firstpass
    namespaceName: DG.Tweening
    className: DOTweenAnimation
  - hash:
      serializedVersion: 2
      Hash: 10ffd5579f941e0f4946756d18f1a9df
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValueTuple
  - hash:
      serializedVersion: 2
      Hash: ae243676f1537b2d26c1846ef08f4d08
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: CustomWagonProfile
  - hash:
      serializedVersion: 2
      Hash: c78ed13f6b5a632d70d4f73f56ad81f2
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: VariablesSaver
  - hash:
      serializedVersion: 2
      Hash: 1a680d9a08de79b0d2c779e78c9843b4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: d1c227ea5598da0ed4598c327a53bff6
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineDecollider
  - hash:
      serializedVersion: 2
      Hash: c7dbf51e84b03c7dfbe6b1b12ee989d2
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePOV
  - hash:
      serializedVersion: 2
      Hash: 5a25c7516fadef3552c99f55215749d0
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: 9c51f4463b63109601ee62546379e986
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: SpeedChangeZone_v3
  - hash:
      serializedVersion: 2
      Hash: 5505ae7d29648ee4e4c62ba1daae3ebd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CustomizationManager
  - hash:
      serializedVersion: 2
      Hash: d9ee128c2eb965dc49901b0756eea2ca
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Mirror
  - hash:
      serializedVersion: 2
      Hash: 7af22626d46eadf94cc9655a7a7477a1
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: RailSensor
  - hash:
      serializedVersion: 2
      Hash: ee7d463adb549a4ed98845d6f0d231a7
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineTrack
  - hash:
      serializedVersion: 2
      Hash: 063582f0f411c22c79f99673aa1e911f
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: BellZone
  - hash:
      serializedVersion: 2
      Hash: 25a70ac19eb2e03345166c9dc471ca06
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: GroupWeightManipulator
  - hash:
      serializedVersion: 2
      Hash: 96e7bcfd2d072992dab64ac44058b930
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCancelMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7d557386247f1dae0a292507110377a8
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSequencerCamera
  - hash:
      serializedVersion: 2
      Hash: b6792907b61f32faaf3e23f55e750107
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessResources
  - hash:
      serializedVersion: 2
      Hash: 415316b9bbc4e339c901a46503e2790b
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: bd2d9b072e71e348486414804becb6fe
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePath
  - hash:
      serializedVersion: 2
      Hash: 406fa6c94315ef31c84aab28b8ea5995
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Neon
  - hash:
      serializedVersion: 2
      Hash: 99bb9140774a8f8cac89436af29b544d
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineDeoccluder
  - hash:
      serializedVersion: 2
      Hash: 0bbce4bd2dcbf14e779b0a67b0b85f8a
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: CollactableScript
  - hash:
      serializedVersion: 2
      Hash: 568d5122d26d6d62d4327fe5feddfe20
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: VehiclePlayerPosition
  - hash:
      serializedVersion: 2
      Hash: c6d40e565fb66d73c39036026ea17ac4
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CinematicCamera
  - hash:
      serializedVersion: 2
      Hash: 24af424a437762b0c98a2238a41b2825
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: ff76cf6cf52b4db7b95af6ffadca9288
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 0f19765290d81f9e93eff7828a091d40
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: a516df451d389c3eba2e0e34abc20d13
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_FixedCamera
  - hash:
      serializedVersion: 2
      Hash: ef112233c216a71f0333b794fc5962f8
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: GunController
  - hash:
      serializedVersion: 2
      Hash: f02d304fad1c46554f9e4d625053bdfd
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_DashboardColors
  - hash:
      serializedVersion: 2
      Hash: e9bfee93ebf3a7afacd9b5a36a892fc0
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: AutoExposure
  - hash:
      serializedVersion: 2
      Hash: b239c0770bc5a00cd9469d6847b8ea32
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: AirplaneCrash
  - hash:
      serializedVersion: 2
      Hash: d7c8092511063e0dae8106bfc8ed63dd
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollMessageListener
  - hash:
      serializedVersion: 2
      Hash: c6539c4c8274f240f4f9835a2fea05fd
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: DemoUI_v3
  - hash:
      serializedVersion: 2
      Hash: 121c49f3362950c7e7d1b15dc46a020e
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: Wagon_v3
  - hash:
      serializedVersion: 2
      Hash: 7c48b4e91326cbf71d4e0cfc47eb2da7
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CustomizationDemo
  - hash:
      serializedVersion: 2
      Hash: 0b63f6d3335e11939ce8790b904c8691
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 76aa43cc6adae8454a66255a8113266d
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: AmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: ec6cf673540b2d4e42c612611eeafe4d
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0a5f4c9fa73d6bf2dee7d464912c1780
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_FOVForCinematicCamera
  - hash:
      serializedVersion: 2
      Hash: 51da7109ee0424542440058d000dbf73
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_Demo_Rotate
  - hash:
      serializedVersion: 2
      Hash: ff5172bc81dc2da4c7de2a8bf56dc4b8
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionsEditorWindow
  - hash:
      serializedVersion: 2
      Hash: e64a691fa0498875177aa18b0829f808
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineShot
  - hash:
      serializedVersion: 2
      Hash: e2ea44611601de69f4be725bfb72be4b
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineCameraOffset
  - hash:
      serializedVersion: 2
      Hash: 5b91242e64b1a13495b40e691e68c478
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: VariableJoystick
  - hash:
      serializedVersion: 2
      Hash: bd520203de5963d63eb4b825c7860a55
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: BakedSegment
  - hash:
      serializedVersion: 2
      Hash: 8a53344512f969f0b1cfc2880b67cd59
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineInputProvider
  - hash:
      serializedVersion: 2
      Hash: 8ec9c9a1dfac5b46c51549ba9eac0a89
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SMR_IgnoredObject
  - hash:
      serializedVersion: 2
      Hash: bf7b4482727ecf39c9fe213fb7bf53ad
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Spoiler
  - hash:
      serializedVersion: 2
      Hash: 8fe6d4f095ce838f5396d1867140a9b6
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 534896cd16790ae23619b486ffd94129
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineSameAsFollowTarget
  - hash:
      serializedVersion: 2
      Hash: 18d0068e1a0748d60d5985cc4fe1e120
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Records
  - hash:
      serializedVersion: 2
      Hash: c0047b8b999968e93c09e5e8bf6bdd51
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Recorder
  - hash:
      serializedVersion: 2
      Hash: d497e277ee09923aa44c51901a8f68f9
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: LensFlareDataSRP
  - hash:
      serializedVersion: 2
      Hash: 59f6cc317a2d8464fcf7d51d2e85b5a5
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: SplineBasedLocomotive
  - hash:
      serializedVersion: 2
      Hash: 30f8af6533f11564d3df5d26aa29998e
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainSuspension
  - hash:
      serializedVersion: 2
      Hash: 0a4ef3c4e73b2f3eef913e2325924084
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: d0392b7f3f52d5af537ade186b4f51f1
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: ae60a1fdb62a9a8ddbf2bb7847498af3
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainController_v3
  - hash:
      serializedVersion: 2
      Hash: 5e380ac35f8f28a804985fecc1c3e679
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Camera
  - hash:
      serializedVersion: 2
      Hash: 9a3a0703d91a459c20b7cc193ea78447
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_DemoMaterials
  - hash:
      serializedVersion: 2
      Hash: e47c10c3a8e13f1409dd2dc8930a20fd
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: SpeedometerScript
  - hash:
      serializedVersion: 2
      Hash: 90aafdbe0a9cd12a778a155bb12982ee
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineCameraManagerEvents
  - hash:
      serializedVersion: 2
      Hash: fb0bb5bb1eebdc96e5bd1410275efb98
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_DashboardObjects
  - hash:
      serializedVersion: 2
      Hash: 049f6aa84fe1cb5359c9338e96d0b07e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: 8bbce9932f67ad8e7f4eefec87800f94
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: 1eab7410e745dbe2df5f084c620afc4e
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: SimpleJoystick
  - hash:
      serializedVersion: 2
      Hash: 3de6f85b27a4d52edc5054990a37fd82
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineGroupFraming
  - hash:
      serializedVersion: 2
      Hash: ad49e20f31ed572e586aa8bbaafb591b
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropMessageListener
  - hash:
      serializedVersion: 2
      Hash: 04b6685b6f724c0d6024e6b6f96c8202
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 6d394de691f6d0187f61c08889353d0e
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnButtonClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: b98001682cba83786775c9e415b89a61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnDropdownValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: b838cbada0b03f1cfbaebc8e124f4f39
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: ee989c3b1cc22890bc1a094097348594
    assemblyName: Unity.Splines
    namespaceName: UnityEngine.Splines
    className: SplineContainer
  - hash:
      serializedVersion: 2
      Hash: bb8d968ef4474e71aaad4a4dce279f90
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnMouseDownMessageListener
  - hash:
      serializedVersion: 2
      Hash: e2c0bb82e08a0cccd28e8358e46cf44b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_InitialSettings
  - hash:
      serializedVersion: 2
      Hash: eb6194e879d2000bd053bde968d2812e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_ColorPickerBySliders
  - hash:
      serializedVersion: 2
      Hash: 7e9cad7f71f9cc52f699bbd2d2a75734
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerClickMessageListener
  - hash:
      serializedVersion: 2
      Hash: ae5a7ab09f79848a1176905784a9df9e
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: GunController
  - hash:
      serializedVersion: 2
      Hash: 5f70b16cd9acc9c1dd88b51b6ed89669
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 5874b789cd9832847c61ebfa803213af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 169208041554637de281c0f7449cb546
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: HelicopterController
  - hash:
      serializedVersion: 2
      Hash: 973907bb2a1fa8d6c22cbd5fab2de2fe
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePositionComposer
  - hash:
      serializedVersion: 2
      Hash: cb49ce80d4b69cb2ed30d83bfcb146ae
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: 92cc25e45712e3f354c19876c4ea5d4e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_AICarController
  - hash:
      serializedVersion: 2
      Hash: 03a8a5f9b33272558c577e8b1559a782
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CrashHammer
  - hash:
      serializedVersion: 2
      Hash: d2ecd94550edf1b56aff07b1f4e41c57
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: b581b4b20fe3f81b59900395fbf027d8
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SMR_MeshGenerationProfile
  - hash:
      serializedVersion: 2
      Hash: 5ebc58ec194dd44630b25fcc270ad4af
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 756c80e827540725c2369a910ae5c76b
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_EmissionBySurface
  - hash:
      serializedVersion: 2
      Hash: 2cc5af0f17cef9c717df88c8c7e4c9aa
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine
    className: LightAnchor
  - hash:
      serializedVersion: 2
      Hash: 46fdccac45c22ce667a76481078b4023
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_PaintManager
  - hash:
      serializedVersion: 2
      Hash: 386244f99d86c1127d3ee446f4f96493
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Decal
  - hash:
      serializedVersion: 2
      Hash: 8515c7dc63c8e1ca3302a40cd29d6dea
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CharacterController
  - hash:
      serializedVersion: 2
      Hash: 5dc51b0140815ee83ba613dce32f5b12
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: 95cec85248d20787021305cfe145ab16
    assemblyName: Unity.Splines
    namespaceName: UnityEngine.Splines
    className: SplineInstantiate
  - hash:
      serializedVersion: 2
      Hash: b5007673bbbdb9d12597a1d8201370f5
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 1f421568061c6c12a666a695cff95bf0
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_ShadowRotConst
  - hash:
      serializedVersion: 2
      Hash: 30678f5b72eebfb66ff81811e1050267
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CameraCarSelection
  - hash:
      serializedVersion: 2
      Hash: 4c10e2ecd2eef188ab4d6943dc13728c
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SMR_MeshCapTag
  - hash:
      serializedVersion: 2
      Hash: d5cd7f207e91ca8336c65a577a6fb2a0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: 3e87b10f8a748ee9793e3d268e3bab8a
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineStateDrivenCamera
  - hash:
      serializedVersion: 2
      Hash: 94272f9df312c2ff5e0a7bf4c6c9499f
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: CustomEventZone
  - hash:
      serializedVersion: 2
      Hash: 0815fc29500b6b97265c640fe15c0a19
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_BehaviorSelector
  - hash:
      serializedVersion: 2
      Hash: f45600bcc886206dd55a3208474aad61
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnScrollbarValueChangedMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00bbd9db8634496d079d17711b2ff7c4
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: 25593d47dedf8bac7f9c57469c439902
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineTriggerAction
  - hash:
      serializedVersion: 2
      Hash: 008e93a72006db5f3d5584029b1cc58a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_SetLayer
  - hash:
      serializedVersion: 2
      Hash: c9b9b11b067e5a76d46638f6d93e49ce
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineHardLookAt
  - hash:
      serializedVersion: 2
      Hash: fce9b835d1e8b08e7ecea8da19f21986
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: CoroutineRunner
  - hash:
      serializedVersion: 2
      Hash: 6217b1287eca7eed313cf0b864249a30
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: fb79e95da8891fc7ac6c36ca6967af23
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 552dca338f20933ce362ebbe559a6aa6
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerExitMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8467936cc53af965d6d33346be4208db
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_APIExample
  - hash:
      serializedVersion: 2
      Hash: 191cd500c627cd6682ef6f70b8e47f2c
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnter2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: ef38c1407dba8c1f5d69f5db61a694fe
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: AnimatorMessageListener
  - hash:
      serializedVersion: 2
      Hash: bd1e6c475a78ef768f51a9c3bb74ad9a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_AssetPaths
  - hash:
      serializedVersion: 2
      Hash: 0193d75f4569c6f2c3fb47fd2cd8397b
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: Gasoline
  - hash:
      serializedVersion: 2
      Hash: b12640dfdb5966813e9dd838893e6c4a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 8d5318ed8488b1fe17c08044ce1b8309
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: e3d4fd43a8500e8b276951a61f88839e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Handling
  - hash:
      serializedVersion: 2
      Hash: e18dfd2b913b09fbbb2405165a2e6a44
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: ada873da50974d1e7ce247ffb296b0f3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 73f751d98018de87335d475465ee242e
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Core
  - hash:
      serializedVersion: 2
      Hash: 5382156409d7c462c67822a5ee8d6c85
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: d85292b1d7ddb10e5a2df5d221dcba86
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 185e7bacfbbc5defefa609c70f7ff4ce
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: f4b6cb7cbb51a55a1baf0f16188d5f16
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_AIWaypointsContainer
  - hash:
      serializedVersion: 2
      Hash: 6255f67b9d6d67ea7aa3e2658a3e8149
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_LOD
  - hash:
      serializedVersion: 2
      Hash: e402a382f213255c22bda9254dd831b3
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: SceneVariables
  - hash:
      serializedVersion: 2
      Hash: c0277466193b6e6691bd27fbe2d7de87
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Neon
  - hash:
      serializedVersion: 2
      Hash: 9a2162ebe7303f13b89ba4fcb5e96459
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: ExtinguishableFire
  - hash:
      serializedVersion: 2
      Hash: 540882762544409cc4530e60db00e951
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: 8e867e9b6da8d6e002a3b7061e5f4ea2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Readme
  - hash:
      serializedVersion: 2
      Hash: 70611138ebf64d9baeaab0fb4628f8d2
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_AIO
  - hash:
      serializedVersion: 2
      Hash: e06a7988d6fc9a101b8d89e8033face3
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: GizmoObject
  - hash:
      serializedVersion: 2
      Hash: 17142b03663387290480c82303274c02
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnPointerEnterMessageListener
  - hash:
      serializedVersion: 2
      Hash: 00eef92bf387da2c9009267123d9d674
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnTriggerExit2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: ed2c59b75f8f3cd8df4cd4387d965e71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: 161a62664f39fa8e8e5d0fe1aeca49fa
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: DecalDestroyer
  - hash:
      serializedVersion: 2
      Hash: 2541c03d117382d4ebbe0bb0775dd2dc
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerProgressBar
  - hash:
      serializedVersion: 2
      Hash: 5c21f4846181ee6e245db1fcfb62fc0e
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: 38d6875f20147bb5967957470fde372f
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_CrashPress
  - hash:
      serializedVersion: 2
      Hash: ca7965796a6fc0e5baad4cb19f5b5ded
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreak2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: c5be3766e4d038e9592329ac1c228cf1
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineTargetGroup
  - hash:
      serializedVersion: 2
      Hash: 9f92e751d2286296361a1d06b9e90c1e
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: ffaaa6b08bc8a8928411e8e402432358
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessVolume
  - hash:
      serializedVersion: 2
      Hash: 8a42fda30872544cc41dd122bff6925c
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineDollyCart
  - hash:
      serializedVersion: 2
      Hash: 12a1c50ef10a9fef9d6f8ad601f84ae8
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: WheelSkidmarks
  - hash:
      serializedVersion: 2
      Hash: cde402e184a6ab530144506bc9d6a0af
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: 9b8be587cb1a818046adc0e7ba7be763
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_DemoVehicles
  - hash:
      serializedVersion: 2
      Hash: f778e23b6ff26b8de4721be0eb1fa240
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStayMessageListener
  - hash:
      serializedVersion: 2
      Hash: cdc3014face6595ca7d614440371e40c
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: EnemyAI
  - hash:
      serializedVersion: 2
      Hash: 3804f59304d6b3618ba420ba4f36b178
    assemblyName: Assembly-CSharp
    namespaceName: Invector
    className: vAnimateUV
  - hash:
      serializedVersion: 2
      Hash: 614f06151ecdef54539c73e98f172d93
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: NaturalAI
  - hash:
      serializedVersion: 2
      Hash: b747f5d40674055dd70e18552f1b0447
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: f9be94fcc88dd411a18da432613ef195
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: FollowTargetCamera
  - hash:
      serializedVersion: 2
      Hash: bf1cce2e164ce51b9f7fd97f728ac9db
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineCollisionImpulseSource
  - hash:
      serializedVersion: 2
      Hash: 549c48377dfc625fd6394a51540f713a
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainPlayerInput
  - hash:
      serializedVersion: 2
      Hash: e6b258489c88376c5869946b2735e7e5
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: d3a50a0967f797f0e08d221ab290e8fe
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: DirectionIndicator
  - hash:
      serializedVersion: 2
      Hash: f6c0167973b271c6fc9bfdc4483b78ec
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: HelicopterSystemManager
  - hash:
      serializedVersion: 2
      Hash: 60611ab578d5058f7eece116b7ff6078
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Splines
    className: SplineMeshRenderer
  - hash:
      serializedVersion: 2
      Hash: d7d424ed392cf89bdce865cf7461334f
    assemblyName: Unity.Splines
    namespaceName: UnityEngine.Splines
    className: SplineAnimate
  - hash:
      serializedVersion: 2
      Hash: 5b73927421989dc53ebeece20259585e
    assemblyName: Assembly-CSharp
    namespaceName: AdvancedHelicopterControllerwithShooting
    className: GyroscopeViewController
  - hash:
      serializedVersion: 2
      Hash: 35b7bab73cb36662865a17627b5ce328
    assemblyName: Assembly-CSharp
    namespaceName: Invector.vCharacterController
    className: vThirdPersonController
  - hash:
      serializedVersion: 2
      Hash: 02b560210ef5de96ec7f00bbc146c1db
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BikeCamera
  - hash:
      serializedVersion: 2
      Hash: 6a0b5b415c00f783202fd1ae2d556cb2
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: bc2ac65b752370fa24a6e58f20b3ea20
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_WheelCamera
  - hash:
      serializedVersion: 2
      Hash: 4c7f8049173a9355f66c243196667e88
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Joystick
  - hash:
      serializedVersion: 2
      Hash: 418d70e5bc2c3c7ab90abefb6fe302ba
    assemblyName: Assembly-CSharp
    namespaceName: CartoonFX
    className: CFXR_Demo
  - hash:
      serializedVersion: 2
      Hash: ee264d44f69caea9d330cc4f04bce59b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_SliderTextReader
  - hash:
      serializedVersion: 2
      Hash: 8e776e5d430cf0c878866026bed7e145
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_BehaviorButton
  - hash:
      serializedVersion: 2
      Hash: b88e78b6860b6061b2c681ea9b80b6bf
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: fe111e830148f07e5735ebe7620f752e
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: db6133687572f9dc24f1fd4238ef9b83
    assemblyName: Unity.TextMeshPro
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: af896ac18239b9607920b8e8842efa67
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: TrainWheel_v3
  - hash:
      serializedVersion: 2
      Hash: f1495c77a7386821056215523f00af1a
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Customizer_Siren
  - hash:
      serializedVersion: 2
      Hash: 8a889661f39163fc91d896a8fc8aea2b
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: JoystickPlayerExample
  - hash:
      serializedVersion: 2
      Hash: 1c57f05ff28b23e14ab5c827c2c5a20d
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: TrainInputSettings
  - hash:
      serializedVersion: 2
      Hash: 48c646ce0442b8e8e8dd0ebbea1a6837
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineComposer
  - hash:
      serializedVersion: 2
      Hash: b48e18a1d4dcdd52ec6ff1219b9d9bf6
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.Cameras
    className: CameraFollow
  - hash:
      serializedVersion: 2
      Hash: 10c17b309bdca62b4d7fbaf113ed4ca0
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 58f6c91dc9977217e7536afccdb74f71
    assemblyName: Unity.RenderPipelines.Core.Runtime
    namespaceName: UnityEngine.Rendering
    className: ProbeAdjustmentVolume
  - hash:
      serializedVersion: 2
      Hash: e55da8f7a38832eacb62f8a79a61553c
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Color
  - hash:
      serializedVersion: 2
      Hash: 0da66c53ba26d37b1eb9cf96ff0df0d7
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: RouteManager
  - hash:
      serializedVersion: 2
      Hash: cad78d28032798503e6ee55dbe92db26
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_Telemetry
  - hash:
      serializedVersion: 2
      Hash: f81b0178d11438bd2b63b09c6dbb2705
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: BikeControl
  - hash:
      serializedVersion: 2
      Hash: 9a174471d4db2c0c6d3ac47d2f818db1
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnParticleCollisionMessageListener
  - hash:
      serializedVersion: 2
      Hash: 0237607a3a697cf130c195c13cb63a88
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: ColorGrading
  - hash:
      serializedVersion: 2
      Hash: 1b00fc26f9cb78681d48ada37281260c
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineExternalCamera
  - hash:
      serializedVersion: 2
      Hash: 83cfda82082c233d96cc677c19f57e07
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnJointBreakMessageListener
  - hash:
      serializedVersion: 2
      Hash: ff086b2fc4609f8b094e7c0435c6db0a
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: PostProcessLayer
  - hash:
      serializedVersion: 2
      Hash: 362a2dcd6340c6145904f19163b522ba
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: OtherVehicleEnterexit
  - hash:
      serializedVersion: 2
      Hash: 49087b2481fd9708ba4c89dd6c6cd5bc
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachinePipeline
  - hash:
      serializedVersion: 2
      Hash: 5318084cc945f6de9a22512b4cb8c712
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_AIBrakeZone
  - hash:
      serializedVersion: 2
      Hash: a1ac4c50e55874de16eee3db71aa9784
    assemblyName: UnityEngine.UI
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: b462ce9b96d7f3f3a8a18557fbf44071
    assemblyName: Unity.Postprocessing.Runtime
    namespaceName: UnityEngine.Rendering.PostProcessing
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: a1ce09c30deab8b34044c363087f4865
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: RCC_UI_Controller
  - hash:
      serializedVersion: 2
      Hash: 4718916586b3818eb69bf65df61f0f3a
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnCollisionStay2DMessageListener
  - hash:
      serializedVersion: 2
      Hash: 7e05a93349f82094c7c3b140b54a3254
    assemblyName: Assembly-CSharp
    namespaceName: WSMGameStudio.RailroadSystem
    className: PassengerTags
  - hash:
      serializedVersion: 2
      Hash: 28dcf9df3c8532286db2ea8e40e52d66
    assemblyName: Unity.Cinemachine
    namespaceName: Unity.Cinemachine
    className: CinemachineHardLockToTarget
  - hash:
      serializedVersion: 2
      Hash: 7ffe9d4d9f3c4a87a1384f902b04d270
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: RadarSystem
  - hash:
      serializedVersion: 2
      Hash: 185038891c578447dd7f1728d014128f
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: SimpleJoystick
  - hash:
      serializedVersion: 2
      Hash: 740bfc9f7ecd2e9c20c60433477d9eb2
    assemblyName: Assembly-CSharp
    namespaceName: BoatControllerwithShooting
    className: RadarItem
  - hash:
      serializedVersion: 2
      Hash: d82086405f46de14829f6b4bcbdd8fc9
    assemblyName: Unity.VisualScripting.Core
    namespaceName: Unity.VisualScripting
    className: UnityOnBecameInvisibleMessageListener
  - hash:
      serializedVersion: 2
      Hash: 800d7bbf0edb0055aeacdbfbe84a84b8
    assemblyName: Unity.InputSystem
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 1e479bea8bd85f729d162118259020c1
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Dronecamera
  - hash:
      serializedVersion: 2
      Hash: f2305da2400dfe6f069770a1a63f0f7c
    assemblyName: Unity.Timeline
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 737e17faf790d408b2bc124eed30063a
    assemblyName: Unity.Splines
    namespaceName: UnityEngine.Splines
    className: SplineExtrude
  - hash:
      serializedVersion: 2
      Hash: 2a4bf9bfa4fc85b7c3fd76767f844af6
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenComponent
  - hash:
      serializedVersion: 2
      Hash: 4f1dec93fa2a0b0e7b8b47518da31b1c
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenSettings
  - hash:
      serializedVersion: 2
      Hash: 67b26c758da0bb5d1c595575f75d8afe
    assemblyName: DOTweenPro.dll
    namespaceName: DG.Tweening
    className: DOTweenVisualManager
  - hash:
      serializedVersion: 2
      Hash: 9b38d1ee1b24f977bba5e3f33a8d3dad
    assemblyName: DOTweenPro.dll
    namespaceName: DG.Tweening
    className: DOTweenPath
  - hash:
      serializedVersion: 2
      Hash: e350eeed062350b382348e9dc7ffbfca
    assemblyName: DemiLib.dll
    namespaceName: DG.DemiLib.External
    className: DeHierarchyComponent
  - hash:
      serializedVersion: 2
      Hash: 6117185c8f0214c98e4ecdae9ed1ab23
    assemblyName: Assembly-CSharp
    namespaceName: 
    className: Dronecameraactivedeactive
  platform: 13
  scenePathNames:
  - Assets/Scenes/TPSFREEHAND.unity
  playerPath: D:/My Project/Driving Simulator Game Z TEC/Apk/Test 9.apk
