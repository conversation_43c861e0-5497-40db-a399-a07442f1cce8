{ "pid": 11572, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11572, "tid": 1, "ts": 1752055171817630, "dur": 4399, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752055171822035, "dur": 36331, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752055171858368, "dur": 975, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 11572, "tid": 4720, "ts": 1752055171886248, "dur": 25, "ph": "X", "name": "", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171817585, "dur": 37844, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855430, "dur": 30238, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855445, "dur": 47, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855498, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855762, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855768, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855928, "dur": 10, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171855941, "dur": 4266, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860211, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860214, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860321, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860325, "dur": 56, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860404, "dur": 2, "ph": "X", "name": "ProcessMessages 82", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860408, "dur": 44, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171860459, "dur": 1017, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171861482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171861484, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171861557, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171861559, "dur": 5594, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171867157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171867160, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171867213, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11572, "tid": 51539607552, "ts": 1752055171867216, "dur": 18443, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11572, "tid": 4720, "ts": 1752055171886278, "dur": 77, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11572, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11572, "tid": 47244640256, "ts": 1752055171817514, "dur": 41892, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11572, "tid": 47244640256, "ts": 1752055171859407, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 47244640256, "ts": 1752055171859409, "dur": 76, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11572, "tid": 4720, "ts": 1752055171886358, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11572, "tid": 42949672960, "ts": 1752055171813057, "dur": 72675, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ts": 1752055171813231, "dur": 3592, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ts": 1752055171885742, "dur": 17, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 42949672960, "ts": 1752055171885761, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11572, "tid": 4720, "ts": 1752055171886369, "dur": 63, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752055171856340, "dur":51, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171856453, "dur":2824, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171859294, "dur":553, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171859895, "dur":1402, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171861419, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":0, "ts":1752055171861343, "dur":137, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171861481, "dur":5690, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171867172, "dur":761, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171867961, "dur":77, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171868304, "dur":11910, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752055171860882, "dur":609, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752055171861505, "dur":1088, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1752055171862634, "dur":4517, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752055171860998, "dur":529, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752055171861528, "dur":1018, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752055171866143, "dur":901, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1752055171862547, "dur":4503, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752055171860985, "dur":523, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752055171861549, "dur":5587, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752055171861077, "dur":469, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752055171861547, "dur":5615, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752055171861124, "dur":446, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752055171861571, "dur":5572, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752055171862213, "dur":4942, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752055171861259, "dur":334, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752055171861594, "dur":5609, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752055171861300, "dur":304, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752055171861605, "dur":5527, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752055171861349, "dur":265, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752055171861614, "dur":5569, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752055171861434, "dur":191, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752055171861625, "dur":5520, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752055171861545, "dur":5622, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752055171861607, "dur":5591, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752055171861688, "dur":5485, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752055171861748, "dur":5404, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752055171861804, "dur":5354, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752055171861882, "dur":5304, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752055171861961, "dur":5188, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752055171862053, "dur":5078, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752055171862127, "dur":5012, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752055171861175, "dur":408, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752055171861584, "dur":5585, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752055171862250, "dur":4930, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752055171862320, "dur":4826, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752055171862372, "dur":4762, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752055171862429, "dur":4748, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752055171885292, "dur":597, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11572, "tid": 4720, "ts": 1752055171886488, "dur": 3602, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11572, "tid": 4720, "ts": 1752055171890139, "dur": 13952, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11572, "tid": 4720, "ts": 1752055171886243, "dur": 17928, "ph": "X", "name": "Write chrome-trace events", "args": {} },
