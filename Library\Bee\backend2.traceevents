{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752054599408880, "dur":94, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599409059, "dur":125673, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599534739, "dur":2828, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599537793, "dur":240, "ph":"X", "name": "<PERSON><PERSON>",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1752054599538034, "dur":1304, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599539464, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":0, "ts":1752054599539531, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":0, "ts":1752054599539893, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":0, "ts":1752054599540016, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-firstpass-FeaturesChecked.txt_ww6f.info" }}
,{ "pid":12345, "tid":0, "ts":1752054599540501, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599541033, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599542444, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":0, "ts":1752054599542795, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599544096, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599544344, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info" }}
,{ "pid":12345, "tid":0, "ts":1752054599544950, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599545307, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599545479, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info" }}
,{ "pid":12345, "tid":0, "ts":1752054599545539, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1752054599545668, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VehiclesModule-FeaturesChecked.txt_a1wc.info" }}
,{ "pid":12345, "tid":0, "ts":1752054599546156, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/lib_burst_generated.so" }}
,{ "pid":12345, "tid":0, "ts":1752054599546274, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1752054599546388, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/stripped/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1752054599546475, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/artifacts/objcopy_5lmi/libil2cpp.dbg.so" }}
,{ "pid":12345, "tid":0, "ts":1752054599546540, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Link_Android_arm64 Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1752054599546621, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":0, "ts":1752054599546694, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9kll8spbuk4q.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599546808, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"IL2CPP_CodeGen D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/il2cpp_conv_dqml.traceevents" }}
,{ "pid":12345, "tid":0, "ts":1752054599547026, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i54a6c2ohxdh.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599547376, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xj7cmp094r3w.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599547814, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599547941, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548005, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548099, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548218, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548404, "dur":92, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548606, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548664, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrq9v26xound.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548809, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tuh2b1fry8rv.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599548915, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fn868ses76d8.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599549060, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mqzzornlhliq.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599549176, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599549323, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599550230, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/17ru68ruxfr6.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599550527, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v6q86bciim52.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599551132, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599552774, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599553116, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599553205, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599553535, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ptdqv50klsp3.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599554870, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599556012, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ps28vfczluqh.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599556144, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvt1belsv8uz.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599556229, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbxprnzpyw6i.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599557419, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599559777, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599560422, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zvidtdy85f7e.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562012, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8zadaggpr090.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562078, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zulbjx00y6hz.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562165, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qlh7yuo39bz.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562265, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lzrbp09zxf4t.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562354, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/009ns7gybvdd.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562448, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/85e7sibqjg3m.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562688, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hfrwh0iewgoy.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599562799, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599563052, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wvonircd98c.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599563158, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599563311, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uuardhox5g5u.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599563512, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599563663, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cc6dokwk1bcr.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599563799, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/56ben38xdc5p.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564280, "dur":134, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564423, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564516, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kmha12tx5jhl.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564636, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/98j4321vll6v.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564750, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564809, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x1eyvuv02wa1.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599564897, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s9o7svgtrjey.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565033, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565181, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zl6xdwmh20f6.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565285, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565342, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2nhwchez4c3y.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565441, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565504, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599565942, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mw9ositkrg7u.o" }}
,{ "pid":12345, "tid":0, "ts":1752054599566486, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/analytics.json" }}
,{ "pid":12345, "tid":0, "ts":1752054599566835, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599568254, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599569311, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__103.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599570052, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599570158, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599570917, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__34.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599571418, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__45.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599573066, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__78.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599573185, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__80.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599573464, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__86.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599576159, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599577819, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599578412, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__4.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599578710, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Splines_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599578900, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599579066, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599579257, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Timeline_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599579538, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AIModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599579841, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599579956, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599580022, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599580125, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599581364, "dur":93, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599581492, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599581630, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599581928, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainPhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599583165, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599583354, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599583411, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599583469, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1752054599584141, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VehiclesModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599584521, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1752054599584841, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":0, "ts":1752054599587122, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":0, "ts":1752054599587286, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752054599587612, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1752054599588631, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainPhysicsModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1752054599539377, "dur":51999, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599591388, "dur":198032, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599789421, "dur":850, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599790271, "dur":74, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599790778, "dur":15381, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752054599539254, "dur":52146, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599596194, "dur":1540, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1752054599597736, "dur":180, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/xamarin-android" }}
,{ "pid":12345, "tid":1, "ts":1752054599597917, "dur":148, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598065, "dur":139, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598204, "dur":118, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598322, "dur":118, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598440, "dur":133, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598573, "dur":126, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598700, "dur":144, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1752054599598845, "dur":8457, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599607302, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599607416, "dur":99, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599607516, "dur":96, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599607613, "dur":95, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599607709, "dur":127, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599607836, "dur":108, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752054599607944, "dur":110, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752054599608055, "dur":101, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752054599608156, "dur":102, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752054599608258, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1752054599608372, "dur":129, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752054599608502, "dur":111, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752054599608613, "dur":105, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752054599608719, "dur":104, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752054599608823, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1752054599608936, "dur":217, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752054599609153, "dur":254, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752054599609407, "dur":245, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752054599609652, "dur":216, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752054599609868, "dur":312, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1752054599610181, "dur":123, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752054599610304, "dur":91, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752054599610395, "dur":91, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752054599610486, "dur":90, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752054599610581, "dur":94, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1752054599610675, "dur":94, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599610769, "dur":92, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599610861, "dur":91, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599610952, "dur":122, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599611075, "dur":106, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1752054599611181, "dur":150, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":1, "ts":1752054599611331, "dur":994, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1752054599612326, "dur":1013, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1752054599613340, "dur":920, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1752054599614260, "dur":106, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-anydpi-v26" }}
,{ "pid":12345, "tid":1, "ts":1752054599614366, "dur":114, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-mdpi" }}
,{ "pid":12345, "tid":1, "ts":1752054599614481, "dur":198, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity" }}
,{ "pid":12345, "tid":1, "ts":1752054599614679, "dur":123, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/Libs/arm64-v8a" }}
,{ "pid":12345, "tid":1, "ts":1752054599614802, "dur":379, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1752054599615181, "dur":339, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1752054599615520, "dur":145, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Android/Data" }}
,{ "pid":12345, "tid":1, "ts":1752054599591403, "dur":24380, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599615832, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":1, "ts":1752054599616078, "dur":2070, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1752054599618149, "dur":1267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599621141, "dur":1598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"IL2CPP_CodeGen D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/il2cpp_conv_dqml.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1752054599622740, "dur":4425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599632519, "dur":7010, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599639561, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599632479, "dur":7295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lh8sv41vny8b.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599640099, "dur":5459, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__81.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599645575, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599640058, "dur":5746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4cjs8k3abv8w.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599645964, "dur":3122, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752054599649108, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599645928, "dur":3239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vfm1o9u8rfhk.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599649681, "dur":6878, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599656570, "dur":156, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599649638, "dur":7088, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wzln42kclvsi.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599656727, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599656870, "dur":3195, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599660083, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599656833, "dur":3482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uj5pvkt515uf.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599660788, "dur":6968, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599667773, "dur":172, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599660701, "dur":7244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezd2p1ujqlmu.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599668154, "dur":4196, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__88.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599672371, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599668114, "dur":4488, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3y41jrzuxbtg.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599672743, "dur":5358, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599678111, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599672697, "dur":5598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/db50b1bh2cpv.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599678389, "dur":6122, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AssetBundleModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599684536, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599678359, "dur":6452, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ig5tdiuec55o.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599684813, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599684961, "dur":3587, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599688566, "dur":180, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599684881, "dur":3866, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ldqhcp8dvt5c.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599688868, "dur":4527, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__74.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599693412, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599688832, "dur":4796, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599693822, "dur":6997, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599700838, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599693773, "dur":7277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ajhot4s4ia6f.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599701051, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599701148, "dur":5111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__8.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599706280, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599701117, "dur":5385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z5rdbpowxtq2.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599706660, "dur":8271, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__10.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599714962, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599706610, "dur":8572, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ac7tbdjsuouj.o" }}
,{ "pid":12345, "tid":1, "ts":1752054599715300, "dur":51, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599716364, "dur":245, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__32.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599716789, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__10.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599717359, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599717572, "dur":274, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__84.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599717855, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752054599717969, "dur":361, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752054599718412, "dur":356, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1752054599719147, "dur":275, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1752054599719423, "dur":70044, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599539403, "dur":52120, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599591530, "dur":2234, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599593765, "dur":1707, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599595472, "dur":20389, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599615960, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599616045, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599616164, "dur":267, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SpriteShapeModule-FeaturesChecked.txt_zrrh.info" }}
,{ "pid":12345, "tid":2, "ts":1752054599616432, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":2, "ts":1752054599616585, "dur":74, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":2, "ts":1752054599616669, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":2, "ts":1752054599616866, "dur":4295, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599621163, "dur":376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752054599621612, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752054599621940, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752054599622170, "dur":534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752054599622734, "dur":293, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Splines-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1752054599623081, "dur":467, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":2, "ts":1752054599623549, "dur":8963, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599632556, "dur":6641, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__2.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599639222, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599632513, "dur":6923, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/giph8s2gk77p.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599639437, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599639540, "dur":5138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__4.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599644704, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599639512, "dur":5403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599645202, "dur":3362, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__89.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599648589, "dur":258, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599645150, "dur":3698, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/46oi46ft1daw.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599648989, "dur":5831, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Collections.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599654838, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599648956, "dur":6067, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3cc7s5m7xd51.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599655247, "dur":563, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Mathematics_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1752054599655827, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599655210, "dur":670, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sk3psbif85dy.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599655952, "dur":4770, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599660751, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599655924, "dur":5064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599661295, "dur":5159, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__103.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599666477, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599661257, "dur":5436, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nbxnh3djstad.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599666774, "dur":5285, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__93.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599672077, "dur":180, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599666746, "dur":5511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x26fhxnnkq0q.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599672342, "dur":7076, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__8.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599679444, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599672304, "dur":7346, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ijh6b3fo2yny.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599679652, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599679703, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ijh6b3fo2yny.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599679845, "dur":2789, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__28.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599682657, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599679778, "dur":3094, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/106avjl0gpni.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599683053, "dur":4029, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599687100, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599682992, "dur":4338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4iks9l8kb4u.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599687631, "dur":5543, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__31.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599693184, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599687481, "dur":5911, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9glks0exwd2t.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599694026, "dur":4024, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__105.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599698070, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599693988, "dur":4317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9ykshs8ezj3b.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599698406, "dur":4980, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599703405, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599698364, "dur":5285, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wp7y7uxo730.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599703967, "dur":5235, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__36.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599709223, "dur":264, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599703922, "dur":5566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599710098, "dur":5360, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__91.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599715480, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599710058, "dur":5644, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bfjbh8srd07r.o" }}
,{ "pid":12345, "tid":2, "ts":1752054599716355, "dur":219, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst.Unsafe.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599716713, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599716942, "dur":167, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__32.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599717619, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599717670, "dur":275, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":2, "ts":1752054599718042, "dur":696, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":2, "ts":1752054599718792, "dur":348, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1752054599719194, "dur":69994, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752054599789240, "dur":221, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599539390, "dur":52087, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599591481, "dur":2336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599593818, "dur":1200, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599595018, "dur":20855, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599615894, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599616105, "dur":261, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info" }}
,{ "pid":12345, "tid":3, "ts":1752054599616441, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":3, "ts":1752054599616606, "dur":80, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml" }}
,{ "pid":12345, "tid":3, "ts":1752054599616688, "dur":51, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml" }}
,{ "pid":12345, "tid":3, "ts":1752054599616749, "dur":53, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":3, "ts":1752054599616814, "dur":4379, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599621194, "dur":256, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1752054599621531, "dur":257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1752054599621870, "dur":635, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1752054599622506, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599622711, "dur":217, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1752054599622986, "dur":210, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1752054599623264, "dur":241, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1752054599623506, "dur":8994, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599632566, "dur":7480, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599640063, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599632524, "dur":7747, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wgxn7sw6k2h.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599640606, "dur":6391, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__5.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599647013, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599640561, "dur":6669, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s20kvqf3nfmk.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599647328, "dur":1351, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1752054599648701, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599647287, "dur":1474, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kmha12tx5jhl.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599648854, "dur":5651, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__48.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599654521, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599648825, "dur":5906, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vxn5g02ymdr4.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599654901, "dur":5273, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599660186, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599654863, "dur":5548, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2jrgs6r4v1zy.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599660813, "dur":1598, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1752054599660747, "dur":1727, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s9o7svgtrjey.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599662746, "dur":7945, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599671213, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599662693, "dur":8753, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mri45r890jp3.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599671542, "dur":2796, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599674348, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599671504, "dur":3029, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/608olej0nq3l.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599674534, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599674833, "dur":9224, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599684080, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599674794, "dur":9500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o4pvni6nxhra.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599684403, "dur":5006, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599689441, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599684365, "dur":5294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ghd97xez3wqy.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599689732, "dur":5542, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__107.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599695298, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599689704, "dur":5795, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vm8whh3a69va.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599695598, "dur":3248, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__2.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599698864, "dur":349, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599695557, "dur":3657, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qxqd40q4j6kr.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599699312, "dur":5111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__27.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599704444, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599699271, "dur":5432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599705029, "dur":5736, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599710783, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599704981, "dur":6031, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t0bcgf387pqw.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599711161, "dur":4762, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599715953, "dur":281, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752054599711121, "dur":5114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" }}
,{ "pid":12345, "tid":3, "ts":1752054599716327, "dur":264, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__26.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599717026, "dur":1199, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__17.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599718948, "dur":395, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__5.cpp" }}
,{ "pid":12345, "tid":3, "ts":1752054599719344, "dur":70114, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599539430, "dur":52177, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599591611, "dur":2250, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599593862, "dur":1695, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599595558, "dur":20262, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599615882, "dur":266, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599616263, "dur":358, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_awp5.info" }}
,{ "pid":12345, "tid":4, "ts":1752054599616624, "dur":71, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v28/styles.xml" }}
,{ "pid":12345, "tid":4, "ts":1752054599616758, "dur":135, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":4, "ts":1752054599616894, "dur":5771, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599622704, "dur":136, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1752054599623334, "dur":9235, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599632603, "dur":5993, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__2.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599638631, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599632570, "dur":6295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jdprqsinlzuw.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599639028, "dur":1188, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599640232, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599638985, "dur":1299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599640500, "dur":1951, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599640454, "dur":2048, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/19kcotfvceq7.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599643351, "dur":4403, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599647772, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599643306, "dur":4692, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599648136, "dur":1464, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Collections_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599649622, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599648071, "dur":1606, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bcwljkgbs91u.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599649839, "dur":2475, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599652330, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599649809, "dur":2740, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599652740, "dur":3437, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__68.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599656195, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599652698, "dur":3707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599656475, "dur":3717, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__33.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599660225, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599656448, "dur":3966, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v48osp9s33us.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599660471, "dur":5497, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__24.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599665988, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599660449, "dur":5777, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9hcuzby93m7g.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599667100, "dur":6329, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599673445, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599667061, "dur":6599, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xgnbosvtj371.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599673740, "dur":7759, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__97.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599681520, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599673706, "dur":8056, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pl1wc5aexsgs.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599681764, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599682012, "dur":6657, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599688688, "dur":174, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599681945, "dur":6917, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599688971, "dur":1165, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599688932, "dur":1246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599690266, "dur":1294, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599691584, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599690232, "dur":1408, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vge3azf3u12w.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599691722, "dur":962, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599692703, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599691689, "dur":1065, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uuardhox5g5u.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599692868, "dur":6696, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599699582, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599692822, "dur":6990, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zals4mmypley.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599699952, "dur":324, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1752054599700292, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599699908, "dur":435, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599700432, "dur":5482, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599705934, "dur":181, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599700392, "dur":5724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j46zdcerhtj0.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599706214, "dur":4123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__84.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599710355, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599706178, "dur":4401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgalojc4k9cf.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599710659, "dur":6338, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__83.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599717021, "dur":290, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752054599710629, "dur":6682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a8p433v5g7vz.o" }}
,{ "pid":12345, "tid":4, "ts":1752054599717458, "dur":296, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__48.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599717954, "dur":828, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599718857, "dur":617, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VehiclesModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1752054599719475, "dur":69979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599539329, "dur":52104, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599591446, "dur":2115, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599593562, "dur":1967, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599595535, "dur":20363, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599615917, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599616043, "dur":479, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/DOTweenPro-FeaturesChecked.txt_dxgf.info" }}
,{ "pid":12345, "tid":5, "ts":1752054599616524, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":5, "ts":1752054599616700, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":5, "ts":1752054599616821, "dur":4322, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599621168, "dur":356, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752054599621589, "dur":469, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752054599622120, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1752054599622425, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599622543, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb" }}
,{ "pid":12345, "tid":5, "ts":1752054599622750, "dur":352, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1752054599623141, "dur":394, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":5, "ts":1752054599623536, "dur":9001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599632590, "dur":723, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":5, "ts":1752054599632538, "dur":819, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jov4p39u0xgj.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599633418, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hfrwh0iewgoy.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599633588, "dur":4728, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599638334, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599633570, "dur":4955, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/06e9mmocdc22.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599638668, "dur":2706, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599641393, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599638621, "dur":2996, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599642001, "dur":4306, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599646326, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599641976, "dur":4554, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ayg1b2oww8v.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599646626, "dur":5223, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__57.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599651862, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599646584, "dur":5477, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hkk09deimueg.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599652155, "dur":5640, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__30.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599657813, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599652124, "dur":5901, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599658110, "dur":3883, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__35.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599662011, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599658078, "dur":4151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hswtse4oaunr.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599662307, "dur":5715, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599668041, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599662270, "dur":5984, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rjj1gat48zbs.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599668402, "dur":5059, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599673478, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599668357, "dur":5368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/34e4vqh3e4xi.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599673835, "dur":4597, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599678447, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599673796, "dur":4861, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfkoufio19j2.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599678755, "dur":8126, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599686898, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599678713, "dur":8417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4agohqpqt00.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599687226, "dur":5080, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599692325, "dur":131, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599687187, "dur":5270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v2ltsjojhjw6.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599692569, "dur":5936, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599698528, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599692529, "dur":6233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lk3p68855ye7.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599698901, "dur":1920, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752054599700840, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599698831, "dur":2066, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ucxyqrog8tbt.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599701083, "dur":2208, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752054599703309, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599701038, "dur":2324, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n96lhotmyyf2.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599703608, "dur":8754, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__31.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599712381, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599703569, "dur":8997, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gvsnt8uqb9un.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599712750, "dur":3267, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__39.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599716040, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599712691, "dur":3572, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" }}
,{ "pid":12345, "tid":5, "ts":1752054599716354, "dur":206, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599716824, "dur":148, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__11.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599717195, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599717386, "dur":225, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__13.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599717618, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752054599717680, "dur":336, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599718054, "dur":423, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599718668, "dur":320, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1752054599719174, "dur":288, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1752054599719463, "dur":70010, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599539415, "dur":52157, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599591579, "dur":2474, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599594054, "dur":21738, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599615817, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":6, "ts":1752054599615970, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599616040, "dur":372, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":6, "ts":1752054599616465, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":6, "ts":1752054599616625, "dur":53, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":6, "ts":1752054599616680, "dur":260, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":6, "ts":1752054599616985, "dur":4185, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599621171, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1752054599621373, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599621498, "dur":213, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Postprocessing.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1752054599621712, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599621961, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1752054599622571, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1752054599622650, "dur":214, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1752054599622894, "dur":695, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1752054599623590, "dur":8914, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599632562, "dur":6505, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599639087, "dur":180, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599632506, "dur":6762, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tleyyt55ari3.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599639353, "dur":5713, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599645083, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599639322, "dur":5987, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/91501rpe8hhy.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599645418, "dur":1378, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataRegistration.c" }}
,{ "pid":12345, "tid":6, "ts":1752054599646817, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599645368, "dur":1500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36ou4anq6mij.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599646962, "dur":778, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752054599647759, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599646921, "dur":896, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a7ezf8z3szds.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599648560, "dur":5087, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599653663, "dur":156, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599648510, "dur":5310, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d3x61gwwi1u8.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599654049, "dur":954, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752054599655015, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599654011, "dur":1062, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kldp9czb34bf.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599655427, "dur":4781, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599660225, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599655387, "dur":5044, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrt9ubex4zyy.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599660725, "dur":6463, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__71.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599667208, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599660676, "dur":6730, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hi2gvrbt7b45.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599667855, "dur":6238, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599674109, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599667812, "dur":6500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599674905, "dur":6638, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599681574, "dur":281, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599674855, "dur":7001, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i54a6c2ohxdh.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599681968, "dur":2914, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599684901, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599681915, "dur":3231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eoeljpbnd8zv.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599685248, "dur":1756, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752054599687021, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599685197, "dur":1884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2qjvvj8f9f4h.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599687274, "dur":4409, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__25.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599691695, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599687236, "dur":4681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uxfiflc76aj0.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599692260, "dur":5656, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__43.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599697934, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599692222, "dur":5911, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599698236, "dur":1812, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599700081, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599698191, "dur":2103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/stosql5gpuhq.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599700463, "dur":4701, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599705183, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599700418, "dur":4977, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fpqqnfhhmpoi.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599705730, "dur":607, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":6, "ts":1752054599706359, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599705692, "dur":721, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4zjw4q9v68e6.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599706489, "dur":5705, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues2.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599712208, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599706463, "dur":5952, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gomkx7xyft0v.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599712419, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599712699, "dur":4692, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599717423, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752054599712651, "dur":5038, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" }}
,{ "pid":12345, "tid":6, "ts":1752054599717776, "dur":353, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1752054599718133, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GuidGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":6, "ts":1752054599718339, "dur":868, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1752054599719208, "dur":70085, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599539473, "dur":52145, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599591622, "dur":2343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599593966, "dur":1758, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599595725, "dur":20128, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599615858, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info" }}
,{ "pid":12345, "tid":7, "ts":1752054599615932, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599616001, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info" }}
,{ "pid":12345, "tid":7, "ts":1752054599616092, "dur":208, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ighi.info" }}
,{ "pid":12345, "tid":7, "ts":1752054599616417, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+44 others)" }}
,{ "pid":12345, "tid":7, "ts":1752054599616722, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599617325, "dur":3826, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599621153, "dur":247, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752054599621482, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752054599621732, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752054599621960, "dur":357, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1752054599622581, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb" }}
,{ "pid":12345, "tid":7, "ts":1752054599622689, "dur":105, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb" }}
,{ "pid":12345, "tid":7, "ts":1752054599622928, "dur":373, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputForUIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1752054599623311, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599623377, "dur":235, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":7, "ts":1752054599623614, "dur":8904, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599632574, "dur":6321, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__3.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599638910, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599632526, "dur":6576, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ps28vfczluqh.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599639183, "dur":3983, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime__1.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599643186, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599639153, "dur":4284, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uq6vwcfgi5ds.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599643709, "dur":4945, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VFXModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599648673, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599643672, "dur":5217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8zadaggpr090.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599648972, "dur":6000, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__15.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599654990, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599648947, "dur":6227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4pl7klguf1rd.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599655232, "dur":1008, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599655213, "dur":1088, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599656686, "dur":6664, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__111.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599663368, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599656640, "dur":6942, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/oiquu7a5omm5.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599663755, "dur":4274, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AccessibilityModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599668047, "dur":256, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599663696, "dur":4608, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a5qnuk1zwuff.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599668386, "dur":1281, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599668350, "dur":1380, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4xx6axaa8uq.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599669810, "dur":4281, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__77.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599674110, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599669781, "dur":4574, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bh1uvuvgsii2.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599674575, "dur":3633, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599678229, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599674528, "dur":3891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hl1iyi40oidl.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599678420, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599678599, "dur":1309, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599679926, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599678534, "dur":1451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/myajzagnq8e9.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599680076, "dur":6178, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__4.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599686273, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599680037, "dur":6442, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7mxwfh15kra1.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599686617, "dur":7142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst.Unsafe.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599693775, "dur":166, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599686574, "dur":7368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8dae7wztktyl.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599694022, "dur":9480, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__17.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599703523, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599693993, "dur":9750, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/alsjg48oldfg.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599703846, "dur":2173, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599706035, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599703791, "dur":2301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/upsfj2k4spt9.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599706173, "dur":602, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599706795, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599706145, "dur":709, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aggn294o7ldd.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599707031, "dur":2021, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599709069, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599706988, "dur":2133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sli71m7v0fnl.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599709235, "dur":1115, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Splines_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1752054599710367, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599709195, "dur":1229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o44r908tjxtl.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599710718, "dur":6201, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__62.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599716938, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599710674, "dur":6482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vv5l2d097ugu.o" }}
,{ "pid":12345, "tid":7, "ts":1752054599717157, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752054599717349, "dur":249, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__20.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599717777, "dur":328, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599718204, "dur":403, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599718646, "dur":656, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":7, "ts":1752054599719303, "dur":70119, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599539532, "dur":52097, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599591634, "dur":2300, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599593935, "dur":575, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599594510, "dur":21306, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599615854, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599616098, "dur":775, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_el85.info" }}
,{ "pid":12345, "tid":8, "ts":1752054599616875, "dur":4297, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599621175, "dur":219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752054599621473, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752054599621736, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599621930, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752054599622193, "dur":426, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1752054599622712, "dur":765, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1752054599623478, "dur":9071, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599632607, "dur":5954, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__44.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599638578, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599632550, "dur":6240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3etolsf89ab3.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599638958, "dur":994, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1752054599639976, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599638925, "dur":1105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9up4h199he7v.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599640111, "dur":4898, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.DirectorModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599645025, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599640074, "dur":5189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m7tsc9fssqjw.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599645265, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599645755, "dur":6324, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__96.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599652091, "dur":145, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599645691, "dur":6546, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/45x8wvwgu448.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599652366, "dur":5037, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599657419, "dur":150, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599652321, "dur":5248, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vrairmxpxc62.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599657871, "dur":4908, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__56.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599662798, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599657814, "dur":5183, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4gcu4zkj62w.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599663090, "dur":3802, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__5.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599666910, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599663054, "dur":4045, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nlgaorbaulc8.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599667211, "dur":4225, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__15.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599671461, "dur":272, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599667170, "dur":4564, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nvfz6xmq45j5.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599671874, "dur":3981, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__32.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599675870, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599671836, "dur":4235, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uwo879suz1dd.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599676266, "dur":4926, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp-firstpass.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599681213, "dur":349, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599676223, "dur":5339, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9kll8spbuk4q.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599681564, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599682347, "dur":4838, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599687214, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599682306, "dur":5135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kdkmybhldeiu.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599687522, "dur":5660, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__17.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599693200, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599687489, "dur":5937, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9i336mhmbwc4.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599693508, "dur":6061, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599699596, "dur":253, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599693470, "dur":6379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x88dxcrw5he9.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599699935, "dur":5315, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__110.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599705269, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599699902, "dur":5614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/32m6eyw7lyam.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599705609, "dur":4010, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599709630, "dur":178, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599705570, "dur":4238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599709885, "dur":5175, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__7.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599715082, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599709855, "dur":5453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0repauku76o0.o" }}
,{ "pid":12345, "tid":8, "ts":1752054599715309, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599716980, "dur":122, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__35.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599717296, "dur":498, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__87.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599717860, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752054599718030, "dur":349, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599718418, "dur":802, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime.cpp" }}
,{ "pid":12345, "tid":8, "ts":1752054599719222, "dur":70194, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599539584, "dur":52056, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599591644, "dur":2364, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599594009, "dur":1793, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599595803, "dur":20067, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599615879, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info" }}
,{ "pid":12345, "tid":9, "ts":1752054599615944, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599616038, "dur":107, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info" }}
,{ "pid":12345, "tid":9, "ts":1752054599616190, "dur":314, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info" }}
,{ "pid":12345, "tid":9, "ts":1752054599616507, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":9, "ts":1752054599616629, "dur":82, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":9, "ts":1752054599616731, "dur":54, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":9, "ts":1752054599616807, "dur":4341, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599621149, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752054599621381, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599621547, "dur":226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752054599621847, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752054599622088, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1752054599622329, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599622718, "dur":387, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1752054599623347, "dur":9134, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599632540, "dur":6406, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599638967, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599632483, "dur":6707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2wexl85vs87e.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599639275, "dur":4725, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599644020, "dur":179, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599639239, "dur":4960, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zulbjx00y6hz.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599644335, "dur":2269, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752054599646623, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599644300, "dur":2380, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/98j4321vll6v.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599646767, "dur":6080, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__17.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599652866, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599646734, "dur":6334, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/77qu6x31cp75.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599653188, "dur":4722, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__3.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599657935, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599653145, "dur":5014, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pf89h968la8d.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599658262, "dur":7053, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTweenPro.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599665339, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599658234, "dur":7326, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/075gzxgtt4dn.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599665561, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599665799, "dur":3307, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__9.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599669121, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599665733, "dur":3573, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ix7inphmu00i.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599669408, "dur":6575, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__94.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599675998, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599669366, "dur":6844, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iia4w61kneca.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599676693, "dur":3445, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599680163, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599676640, "dur":3717, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w9jen0mqna8j.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599680437, "dur":5186, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTween__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599685650, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599680406, "dur":5484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tq14glgmzkkf.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599685975, "dur":4578, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__70.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599690570, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599685944, "dur":4812, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w2ysfixv4nsm.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599691292, "dur":4523, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__95.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599695833, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599691258, "dur":4815, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3xdscggiq2nt.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599696173, "dur":7270, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__86.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599703463, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599696132, "dur":7539, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbhxj24yql0h.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599703753, "dur":3260, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__42.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599707029, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599703726, "dur":3496, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hr5jl3kc3ki4.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599707432, "dur":4077, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__34.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599711529, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599707396, "dur":4355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nyjg963jlnz5.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599711925, "dur":2569, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__19.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599714516, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599711889, "dur":2866, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":9, "ts":1752054599714757, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752054599715546, "dur":86, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":9, "ts":1752054599716350, "dur":158, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599717070, "dur":414, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__59.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599717575, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__16.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599717676, "dur":671, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599718482, "dur":377, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1752054599718946, "dur":386, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__1.cpp" }}
,{ "pid":12345, "tid":9, "ts":1752054599719334, "dur":70092, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599539644, "dur":52008, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599591657, "dur":2266, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599593924, "dur":870, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599594794, "dur":21049, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599615846, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":10, "ts":1752054599616034, "dur":203, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VideoModule-FeaturesChecked.txt_2ixs.info" }}
,{ "pid":12345, "tid":10, "ts":1752054599616327, "dur":592, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info" }}
,{ "pid":12345, "tid":10, "ts":1752054599616921, "dur":4242, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599621164, "dur":491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1752054599621729, "dur":333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1752054599622063, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599622757, "dur":323, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Cinemachine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1752054599623142, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1752054599623135, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Generate Manifests Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt (+3 others)" }}
,{ "pid":12345, "tid":10, "ts":1752054599623472, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599623541, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ModifyAndroidProjectCallback D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)" }}
,{ "pid":12345, "tid":10, "ts":1752054599623654, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599623757, "dur":8735, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599632543, "dur":7624, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__7.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599640180, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599632504, "dur":7906, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wbnw19jb1pmh.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599640676, "dur":5882, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__6.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599646577, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599640633, "dur":6157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bwj8l05dy1ay.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599646791, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599646904, "dur":1607, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":10, "ts":1752054599648529, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599646868, "dur":1723, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yac4a4p3l7bk.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599648682, "dur":7420, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__5.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599656118, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599648648, "dur":7668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b3veiy14s00c.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599656911, "dur":3808, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__46.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599660740, "dur":257, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599656868, "dur":4130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/99z0gb82fny5.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599661256, "dur":4395, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599665671, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599661211, "dur":4666, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h4q9bzdazs6h.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599665878, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599666000, "dur":5786, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__73.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599671810, "dur":183, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599665957, "dur":6037, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/35plvg6oe2w4.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599672084, "dur":6193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599678298, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599672043, "dur":6499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599678649, "dur":3632, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__40.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599682305, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599678605, "dur":3905, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v3hfleqgwup5.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599682627, "dur":8877, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__79.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599691523, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599682593, "dur":9117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hc86qdobyu54.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599691810, "dur":6339, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__47.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599698167, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599691763, "dur":6607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aikh1qo1jpfl.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599698840, "dur":5566, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599704422, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599698793, "dur":5840, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599704847, "dur":6829, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__82.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599711694, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599704814, "dur":7085, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psmgouh8djkg.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599712070, "dur":2395, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__16.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599714485, "dur":293, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599712022, "dur":2757, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p5ov0uurip3w.o" }}
,{ "pid":12345, "tid":10, "ts":1752054599716348, "dur":120, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599716967, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599717037, "dur":547, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__16.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599717592, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752054599717767, "dur":319, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Timeline__1.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599718206, "dur":1111, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":10, "ts":1752054599719319, "dur":70111, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599539700, "dur":51964, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599591669, "dur":2357, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599594027, "dur":1682, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599595710, "dur":20129, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599615888, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599616113, "dur":268, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_15vy.info" }}
,{ "pid":12345, "tid":11, "ts":1752054599616384, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":11, "ts":1752054599616529, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":11, "ts":1752054599616659, "dur":119, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":11, "ts":1752054599616780, "dur":232, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":11, "ts":1752054599617013, "dur":4142, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599621159, "dur":372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1752054599621626, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1752054599621845, "dur":944, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599622979, "dur":154, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.pdb" }}
,{ "pid":12345, "tid":11, "ts":1752054599623162, "dur":154, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":11, "ts":1752054599623323, "dur":9161, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599632535, "dur":5962, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__6.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599638538, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599632486, "dur":6263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599638836, "dur":2973, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__45.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599641830, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599638800, "dur":3244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ouwc11t5czmm.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599642207, "dur":5104, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__34.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599647334, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599642181, "dur":5386, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gkcd8ynq6r6b.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599647742, "dur":4739, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__61.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599652499, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599647702, "dur":5036, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ag2xm4lc564a.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599652740, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599653007, "dur":5204, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599658227, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599652966, "dur":5481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kcj9lts71wxr.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599658546, "dur":4561, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599663129, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599658514, "dur":4818, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599663448, "dur":6473, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599669948, "dur":173, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599663402, "dur":6721, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zvidtdy85f7e.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599670212, "dur":6325, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599676555, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599670174, "dur":6574, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yms2p2c88w6x.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599676992, "dur":3322, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__87.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599680349, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599676952, "dur":3638, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ij1d4hlxunhx.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599681310, "dur":6184, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__5.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599687519, "dur":253, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599681257, "dur":6516, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgqdw46uskjg.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599688050, "dur":3866, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__33.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599691940, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599688007, "dur":4129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mqzzornlhliq.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599692269, "dur":3959, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__14.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599696245, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599692223, "dur":4257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3h1q05zvbzay.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599696481, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599696625, "dur":4500, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__1.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599701143, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599696580, "dur":4797, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5hnporc7gi97.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599701727, "dur":6004, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__98.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599707742, "dur":180, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599701687, "dur":6235, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a14ksepb0gsb.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599708167, "dur":3463, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__16.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599711673, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599708124, "dur":3783, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ajnm70n7tvo.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599712146, "dur":4911, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__2.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599717081, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752054599712086, "dur":5234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4czkg6x1i6ey.o" }}
,{ "pid":12345, "tid":11, "ts":1752054599717483, "dur":1061, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__74.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599718632, "dur":118, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":11, "ts":1752054599718882, "dur":351, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":11, "ts":1752054599719235, "dur":70174, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599539732, "dur":51948, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599591685, "dur":2427, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599594113, "dur":21676, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599615816, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android/d8kzr/_dummy_for_header_discovery" }}
,{ "pid":12345, "tid":12, "ts":1752054599616044, "dur":177, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info" }}
,{ "pid":12345, "tid":12, "ts":1752054599616247, "dur":348, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y5my.info" }}
,{ "pid":12345, "tid":12, "ts":1752054599616599, "dur":66, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":12, "ts":1752054599616717, "dur":238, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":12, "ts":1752054599616995, "dur":4162, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599621172, "dur":308, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752054599621481, "dur":311, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599621849, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752054599622102, "dur":738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1752054599623052, "dur":466, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":12, "ts":1752054599623520, "dur":8977, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599632552, "dur":9193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__106.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599641755, "dur":164, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599632499, "dur":9420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a74817od3lkr.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599642067, "dur":1048, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752054599643134, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599642036, "dur":1155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c4dabwex14fm.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599643384, "dur":4004, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599647408, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599643335, "dur":4298, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jbm9bjl27jgz.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599647856, "dur":4523, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__6.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599652407, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599647808, "dur":4850, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fcb1uk6w4idh.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599652826, "dur":1731, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Timeline_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752054599654578, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599652767, "dur":1865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/56ben38xdc5p.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599655286, "dur":5160, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__99.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599660464, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599655244, "dur":5462, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b9o1cu593i9q.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599660949, "dur":4813, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__59.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599665784, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599660911, "dur":5103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3774928dtk7e.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599666139, "dur":5069, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__100.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599671224, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599666105, "dur":5338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g85u144pif6u.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599671539, "dur":6077, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime__2.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599677632, "dur":145, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599671497, "dur":6281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f69jcmtdltpb.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599677980, "dur":1422, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752054599679420, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599677939, "dur":1534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e7m8oxn5tfdg.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599679649, "dur":5716, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__2.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599685384, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599679586, "dur":6008, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lz9rfs69d1h2.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599685760, "dur":6093, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__90.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599691882, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599685649, "dur":6493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a7yuwsct9xqc.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599692232, "dur":4647, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Mathematics.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599696902, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599692201, "dur":4939, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/83b95fuxpl22.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599697256, "dur":1287, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752054599698566, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599697211, "dur":1422, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4ka0mlsvwx1.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599698801, "dur":6832, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599705653, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599698757, "dur":7118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/55wal3sioh2n.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599706283, "dur":5467, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599711768, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599706227, "dur":5774, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m20qes3fkulp.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599712110, "dur":1778, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule_CodeGen.c" }}
,{ "pid":12345, "tid":12, "ts":1752054599713909, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599712064, "dur":1903, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4ivjxtzymil.o" }}
,{ "pid":12345, "tid":12, "ts":1752054599716345, "dur":106, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__4.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599716516, "dur":254, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599717124, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599717367, "dur":55, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__50.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599717429, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752054599717693, "dur":768, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599718500, "dur":184, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599718721, "dur":406, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":12, "ts":1752054599719168, "dur":281, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__3.cpp" }}
,{ "pid":12345, "tid":12, "ts":1752054599719450, "dur":70012, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599539777, "dur":51918, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599591700, "dur":2347, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599594048, "dur":21782, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599615932, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599616231, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599616284, "dur":481, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info" }}
,{ "pid":12345, "tid":13, "ts":1752054599616767, "dur":4379, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599621147, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752054599621545, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752054599621786, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752054599621927, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599622182, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":13, "ts":1752054599622452, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599622708, "dur":196, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.pdb" }}
,{ "pid":12345, "tid":13, "ts":1752054599622938, "dur":312, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":13, "ts":1752054599623322, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":13, "ts":1752054599623400, "dur":9089, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599632549, "dur":405, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":13, "ts":1752054599632491, "dur":511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599633074, "dur":9793, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599642877, "dur":143, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599633041, "dur":9980, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cczik646utbr.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599643508, "dur":4516, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599648042, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599643455, "dur":4812, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d9rg6ujesit9.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599648369, "dur":3803, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__27.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599652184, "dur":135, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599648338, "dur":3981, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nzaqginwsw4s.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599652443, "dur":8586, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__7.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599661051, "dur":435, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599652402, "dur":9084, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbxprnzpyw6i.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599661609, "dur":6582, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599668210, "dur":257, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599661568, "dur":6899, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599668760, "dur":5734, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599674512, "dur":155, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599668718, "dur":5950, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599674742, "dur":3864, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599678634, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599674709, "dur":4149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hu59tggcxxy3.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599678956, "dur":4141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599683118, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599678923, "dur":4416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yzxvdadx1wuw.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599683473, "dur":6681, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__2.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599690167, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599683430, "dur":6942, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/70zru6g5q2rj.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599690460, "dur":4178, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__29.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599694653, "dur":153, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599690430, "dur":4376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/28qrfz1ykr4x.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599695246, "dur":1084, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":13, "ts":1752054599696356, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599695203, "dur":1207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a951q8orlkl2.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599696554, "dur":7574, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599704145, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599696465, "dur":7871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8zg0e63qyl8i.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599704424, "dur":4441, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__30.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599708880, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599704389, "dur":4676, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fn868ses76d8.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599709145, "dur":3995, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599713159, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599709116, "dur":4262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599713467, "dur":3714, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__21.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599717211, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752054599713433, "dur":4024, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599717484, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":13, "ts":1752054599717667, "dur":255, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599717962, "dur":884, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":13, "ts":1752054599718884, "dur":372, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":13, "ts":1752054599719258, "dur":70155, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599539828, "dur":51889, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599591724, "dur":2409, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599594133, "dur":21750, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599615888, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599616033, "dur":245, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VFXModule-FeaturesChecked.txt_r8mm.info" }}
,{ "pid":12345, "tid":14, "ts":1752054599616444, "dur":1156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":14, "ts":1752054599617668, "dur":3499, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599621184, "dur":387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1752054599621652, "dur":479, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1752054599622345, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599622709, "dur":705, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":14, "ts":1752054599623416, "dur":9125, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599632591, "dur":8005, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__4.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599640612, "dur":160, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599632546, "dur":8227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wpbgkm2rrn3.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599640984, "dur":3435, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__104.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599644438, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599640939, "dur":3710, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ojh41iuhwvrs.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599644746, "dur":5472, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599650229, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599644710, "dur":5682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uw7cfbsz1u1x.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599650480, "dur":4606, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599655103, "dur":136, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599650443, "dur":4797, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tqqyoib0g7mb.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599655646, "dur":5800, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599661461, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599655602, "dur":6045, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599661648, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599661792, "dur":4424, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1752054599666236, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599661756, "dur":4535, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ijiwlknrkr7.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599666416, "dur":6037, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599672468, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599666381, "dur":6249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f3yij4ajqdbi.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599672697, "dur":6770, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__8.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599679484, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599672670, "dur":7033, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xj7cmp094r3w.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599679830, "dur":7237, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__69.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599687093, "dur":271, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599679787, "dur":7578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599687508, "dur":4972, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599692491, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599687457, "dur":5210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xqgs9zqegqf7.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599692803, "dur":1922, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1752054599692758, "dur":2014, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mw9ositkrg7u.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599694772, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599694872, "dur":3336, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599698231, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599694842, "dur":3632, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599698598, "dur":5906, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__3.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599704521, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599698545, "dur":6211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mckl3g7t98d1.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599704840, "dur":5150, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__23.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599710016, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599704807, "dur":5433, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o285j2jc1yvx.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599710512, "dur":4405, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__7.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599714938, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599710476, "dur":4683, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eqopvfazu5ej.o" }}
,{ "pid":12345, "tid":14, "ts":1752054599715161, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599715507, "dur":70, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1752054599716508, "dur":200, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__29.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599717130, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__71.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599717386, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752054599717498, "dur":318, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__80.cpp" }}
,{ "pid":12345, "tid":14, "ts":1752054599718416, "dur":868, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1752054599719285, "dur":70171, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599539887, "dur":51849, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599591741, "dur":2332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599594074, "dur":21805, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599615928, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599616178, "dur":304, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_pwj8.info" }}
,{ "pid":12345, "tid":15, "ts":1752054599616484, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":15, "ts":1752054599616632, "dur":50, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":15, "ts":1752054599616767, "dur":215, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":15, "ts":1752054599616984, "dur":4221, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599621206, "dur":663, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1752054599621870, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599621967, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1752054599622199, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1752054599622500, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599622721, "dur":249, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VehiclesModule.pdb" }}
,{ "pid":12345, "tid":15, "ts":1752054599622998, "dur":192, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":15, "ts":1752054599623347, "dur":97, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/analytics.json" }}
,{ "pid":12345, "tid":15, "ts":1752054599623446, "dur":9069, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599632576, "dur":5926, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__109.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599638524, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599632519, "dur":6216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/haw1h6x6c0o9.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599639059, "dur":4318, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__50.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599643395, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599639013, "dur":4608, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25hyt1fxyzo0.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599643739, "dur":837, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":15, "ts":1752054599644595, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599643686, "dur":964, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/85e7sibqjg3m.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599644724, "dur":4265, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__7.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599649009, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599644695, "dur":4543, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/unvu3uwlnpzy.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599649420, "dur":6561, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__12.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599655993, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599649384, "dur":6845, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xmue5m705mnv.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599656521, "dur":1518, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp-firstpass_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1752054599658056, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599656474, "dur":1641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lzrbp09zxf4t.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599658767, "dur":2344, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1752054599658716, "dur":2455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599661278, "dur":5399, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599666697, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599661237, "dur":5672, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bdu1ja51muwa.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599666991, "dur":6848, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__41.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599673857, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599666959, "dur":7124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599674268, "dur":5392, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__4.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599679679, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599674204, "dur":5719, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a85wbpqwoi20.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599679925, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599680030, "dur":4283, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__92.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599684343, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599679995, "dur":4584, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wumzwh7ogojb.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599684798, "dur":5666, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599690480, "dur":155, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599684754, "dur":5882, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6edgv1k7gptl.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599690714, "dur":1917, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1752054599692653, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599690685, "dur":2030, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2nhwchez4c3y.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599692784, "dur":1664, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1752054599692756, "dur":1738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599694847, "dur":3447, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599698314, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599694812, "dur":3725, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i6hutl07x9cu.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599698538, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599698866, "dur":5696, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__52.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599704584, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599698823, "dur":5972, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot52tqat5phz.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599704867, "dur":4345, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599709235, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599704840, "dur":4614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0324vdc2h4ue.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599709933, "dur":4585, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599714540, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599709875, "dur":4940, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" }}
,{ "pid":12345, "tid":15, "ts":1752054599716397, "dur":295, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599717013, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599717188, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752054599717401, "dur":221, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__44.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599717675, "dur":637, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":15, "ts":1752054599718484, "dur":132, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":15, "ts":1752054599718717, "dur":694, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":15, "ts":1752054599719412, "dur":70025, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752054599539944, "dur":51807, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752054599591755, "dur":2408, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752054599594164, "dur":21631, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752054599615813, "dur":172963, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":16, "ts":1752054599788819, "dur":322, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link_Android_arm64 Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" }}
,{ "pid":12345, "tid":16, "ts":1752054599789247, "dur":228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599540166, "dur":51654, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599591825, "dur":2430, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599594256, "dur":21545, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599615847, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Timeline-FeaturesChecked.txt_in83.info" }}
,{ "pid":12345, "tid":17, "ts":1752054599615936, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599616173, "dur":289, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info" }}
,{ "pid":12345, "tid":17, "ts":1752054599616464, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":17, "ts":1752054599616628, "dur":56, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":17, "ts":1752054599616686, "dur":167, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":17, "ts":1752054599616855, "dur":4284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599621145, "dur":198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1752054599621344, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599621585, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1752054599621958, "dur":649, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1752054599622727, "dur":393, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityLinkerToEditorData.json" }}
,{ "pid":12345, "tid":17, "ts":1752054599623328, "dur":9158, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599632541, "dur":7344, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VehiclesModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599639903, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599632488, "dur":7661, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x76fzfo8l23p.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599640241, "dur":4510, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599644766, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599640210, "dur":4749, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/82lraxe6dit2.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599645186, "dur":1486, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599646691, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599645144, "dur":1601, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599646840, "dur":1027, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599647885, "dur":65, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599646792, "dur":1159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9nmkg3j9skao.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599648048, "dur":1747, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599649813, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599647998, "dur":1877, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kvewo88f22kk.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599650332, "dur":1458, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599650292, "dur":1548, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2mhn039af6b9.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599651920, "dur":5754, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__19.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599657697, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599651886, "dur":6008, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v6q86bciim52.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599658033, "dur":2721, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\DOTweenPro_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599660779, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599657995, "dur":2838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/009ns7gybvdd.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599660920, "dur":4310, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__4.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599665257, "dur":238, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599660883, "dur":4612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25cm8hxcrx0o.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599665878, "dur":4397, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__38.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599670292, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599665833, "dur":4667, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ovnm8cdik8r9.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599670501, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599671502, "dur":4411, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__10.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599675938, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599671461, "dur":4727, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u401qaj1n1mo.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599676283, "dur":5113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__2.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599681413, "dur":169, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599676247, "dur":5335, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4z4l2zsbjmwu.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599681584, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599681719, "dur":6169, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599687908, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599681670, "dur":6462, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v8iwhyj1c5oc.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599688485, "dur":982, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599689485, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599688456, "dur":1086, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r1w5t531a50x.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599689623, "dur":4109, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599693755, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599689594, "dur":4363, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vn7cdxf0399v.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599694194, "dur":5256, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599699472, "dur":256, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599694148, "dur":5581, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n916fvr6k1u4.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599699888, "dur":2140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.ParticleSystemModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599702043, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599699853, "dur":2246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jf3wjb0lbtlr.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599702181, "dur":4776, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__4.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599706976, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599702151, "dur":5041, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p8v3lbbirbiz.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599707299, "dur":8444, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599715767, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599707254, "dur":8699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sk29ap2btmg.o" }}
,{ "pid":12345, "tid":17, "ts":1752054599715954, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752054599716365, "dur":287, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__2.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599717027, "dur":559, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__66.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599717614, "dur":543, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__14.cpp" }}
,{ "pid":12345, "tid":17, "ts":1752054599718406, "dur":261, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599718716, "dur":668, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst.Unsafe_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1752054599719385, "dur":70093, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599540004, "dur":51769, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599591781, "dur":1843, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599593624, "dur":1415, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599595039, "dur":20795, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599615836, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Postprocessing.Runtime-FeaturesChecked.txt_6gzn.info" }}
,{ "pid":12345, "tid":18, "ts":1752054599616022, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599616210, "dur":349, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info" }}
,{ "pid":12345, "tid":18, "ts":1752054599616562, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":18, "ts":1752054599616710, "dur":129, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":18, "ts":1752054599616841, "dur":4350, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599621192, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1752054599621508, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1752054599621743, "dur":248, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1752054599622077, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1752054599622384, "dur":424, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsModule.pdb" }}
,{ "pid":12345, "tid":18, "ts":1752054599622921, "dur":570, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":18, "ts":1752054599623492, "dur":9032, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599632596, "dur":7002, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599639616, "dur":165, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599632539, "dur":7243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ehcxqa0cwkap.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599639864, "dur":3402, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599643283, "dur":190, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599639837, "dur":3637, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ibt2f73hq3tq.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599643574, "dur":6086, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__75.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599649678, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599643540, "dur":6352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ptdqv50klsp3.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599650480, "dur":6526, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599657022, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599650445, "dur":6766, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599657289, "dur":2059, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752054599659367, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599657269, "dur":2154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nyi813yiv8kp.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599660111, "dur":5684, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599665825, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599660067, "dur":5972, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pgj75bd160pu.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599666243, "dur":7229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__54.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599673490, "dur":261, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599666203, "dur":7548, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aht7tf5ocgay.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599673841, "dur":8046, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__108.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599681909, "dur":272, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599673803, "dur":8379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g2blxqh88x7t.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599682343, "dur":5992, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__3.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599688355, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599682309, "dur":6244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/88j8emx3u48y.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599689176, "dur":4597, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__26.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599693794, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599689140, "dur":4879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v5zyz0s5ad0f.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599694020, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599694202, "dur":4120, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AccessibilityModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752054599698350, "dur":68, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599694172, "dur":4247, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9oigfpdat6n.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599698523, "dur":1366, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VFXModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752054599699909, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599698479, "dur":1487, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sszjk6y5r3mc.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599700851, "dur":5875, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__13.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599706746, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599700814, "dur":6142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/17ru68ruxfr6.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599707187, "dur":3831, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599711028, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599707129, "dur":4078, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pn5tr6jvsup6.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599711368, "dur":5806, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__76.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599717215, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599711322, "dur":6138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a9gebffiog1m.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599717494, "dur":160, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a9gebffiog1m.o" }}
,{ "pid":12345, "tid":18, "ts":1752054599717689, "dur":373, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":18, "ts":1752054599718210, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752054599718313, "dur":521, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752054599718884, "dur":387, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1752054599719272, "dur":70146, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599540058, "dur":51731, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599591795, "dur":2236, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599594032, "dur":539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599594572, "dur":21295, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599615873, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_5voi.info" }}
,{ "pid":12345, "tid":19, "ts":1752054599615939, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599616169, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599616388, "dur":387, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":19, "ts":1752054599616777, "dur":4410, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599621188, "dur":501, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1752054599621777, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1752054599622116, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1752054599622442, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AccessibilityModule.pdb" }}
,{ "pid":12345, "tid":19, "ts":1752054599622707, "dur":176, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AccessibilityModule.pdb" }}
,{ "pid":12345, "tid":19, "ts":1752054599623047, "dur":554, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":19, "ts":1752054599623602, "dur":8905, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599632560, "dur":8493, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599641075, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599632513, "dur":8756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599641349, "dur":5587, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599646955, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599641307, "dur":5868, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/irjo0akm51pw.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599647250, "dur":6835, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599654104, "dur":164, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599647218, "dur":7051, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gy0yzb544nxj.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599654420, "dur":5289, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__22.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599659730, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599654383, "dur":5560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599660022, "dur":905, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1752054599660946, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599659989, "dur":1019, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x1eyvuv02wa1.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599661106, "dur":5597, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599666722, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599661067, "dur":5891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nqp3qp5ad6sw.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599667212, "dur":6371, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__80.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599673600, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599667164, "dur":6654, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/34ybo5mc7srh.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599673819, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599674140, "dur":5792, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__4.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599679948, "dur":181, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599674102, "dur":6027, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qnsq2on7pcpt.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599680131, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599680326, "dur":4029, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__101.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599684374, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599680278, "dur":4318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hgqbmklomhna.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599684597, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599684938, "dur":6001, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppReversePInvokeWrapperTable.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599690959, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599684859, "dur":6297, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/01zek1xq1lza.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599691489, "dur":5419, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599696926, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599691450, "dur":5691, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ykst251hxq25.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599697267, "dur":6549, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__7.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599703868, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599697216, "dur":6880, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/983eog650b56.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599704174, "dur":4865, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599709058, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599704140, "dur":5148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/coeqiwfgefg2.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599709374, "dur":3547, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__49.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599712939, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599709345, "dur":3804, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gdrtw9gwp12n.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599713699, "dur":1396, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1752054599715118, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599713586, "dur":1595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tyrz3lym5pkq.o" }}
,{ "pid":12345, "tid":19, "ts":1752054599715561, "dur":134, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599716988, "dur":95, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__41.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599717214, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752054599717781, "dur":359, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__15.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599718182, "dur":507, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1752054599718718, "dur":307, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599719159, "dur":279, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":19, "ts":1752054599719439, "dur":70013, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599540109, "dur":51694, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599591808, "dur":2257, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599594066, "dur":21732, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599615856, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599616283, "dur":407, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GridModule-FeaturesChecked.txt_5p9t.info" }}
,{ "pid":12345, "tid":20, "ts":1752054599616743, "dur":163, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":20, "ts":1752054599616907, "dur":4282, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599621190, "dur":656, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1752054599622761, "dur":334, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":20, "ts":1752054599623134, "dur":133, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":20, "ts":1752054599623356, "dur":9122, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599632529, "dur":4494, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__16.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599637055, "dur":166, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599632481, "dur":4740, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6z6dmin38mqq.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599637309, "dur":3947, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599641273, "dur":177, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599637276, "dur":4175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599641524, "dur":3536, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.ParticleSystemModule.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599645076, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599641491, "dur":3800, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sqf1sldz1ha.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599645686, "dur":6288, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Timeline__1.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599651993, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599645641, "dur":6576, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2n3g8s5g95on.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599652218, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599652382, "dur":668, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752054599652341, "dur":756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599653203, "dur":1158, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AssetBundleModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752054599653161, "dur":1267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6386mtmlure2.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599654544, "dur":4237, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__51.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599658801, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599654506, "dur":4490, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599659273, "dur":1298, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Burst.Unsafe_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752054599660588, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599659232, "dur":1406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s2bwaz6dxjth.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599660714, "dur":5364, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Splines.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599666103, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599660677, "dur":5650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/k4kibxippfaw.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599666443, "dur":5389, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__53.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599671851, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599666400, "dur":5628, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/988q9zoq8dti.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599672099, "dur":5009, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__60.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599677127, "dur":158, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599672064, "dur":5222, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599677417, "dur":4241, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599681687, "dur":595, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599677379, "dur":4904, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/l5t0o7id251b.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599682380, "dur":4181, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__67.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599686579, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599682341, "dur":4479, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599686828, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599686949, "dur":1995, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599688964, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599686901, "dur":2304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxnazyrk9929.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599689363, "dur":6403, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599695792, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599689323, "dur":6674, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rodfxnqmp95i.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599696106, "dur":6874, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599702997, "dur":181, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599696061, "dur":7118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qpxrszza8ri.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599703249, "dur":4444, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599707710, "dur":193, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599703221, "dur":4682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/snuim7hzfdjp.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599708286, "dur":5251, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599713555, "dur":193, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599708252, "dur":5496, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vomfw8rdeo41.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599713841, "dur":2048, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752054599715913, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599713805, "dur":2163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" }}
,{ "pid":12345, "tid":20, "ts":1752054599716383, "dur":433, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599716994, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752054599717052, "dur":393, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__19.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599717488, "dur":759, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__18.cpp" }}
,{ "pid":12345, "tid":20, "ts":1752054599718315, "dur":607, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752054599719009, "dur":350, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1752054599719361, "dur":70082, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599540213, "dur":51619, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599591837, "dur":2562, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599594400, "dur":21477, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599615878, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info" }}
,{ "pid":12345, "tid":21, "ts":1752054599615938, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599616040, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599616239, "dur":339, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_zatd.info" }}
,{ "pid":12345, "tid":21, "ts":1752054599616580, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":21, "ts":1752054599616762, "dur":51, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":21, "ts":1752054599616827, "dur":4322, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599621155, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752054599621437, "dur":294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752054599621813, "dur":571, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1752054599622385, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599622576, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":21, "ts":1752054599622656, "dur":166, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":21, "ts":1752054599622889, "dur":316, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":21, "ts":1752054599623279, "dur":150, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Core.dll" }}
,{ "pid":12345, "tid":21, "ts":1752054599623431, "dur":9063, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599632542, "dur":7186, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__10.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599639740, "dur":164, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599632495, "dur":7410, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bz694xc5ywuw.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599639985, "dur":5129, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__58.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599645132, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599639940, "dur":5431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2znopnsfpiqz.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599646268, "dur":6384, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__12.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599652677, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599646222, "dur":6693, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gso6qzdxurfn.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599653044, "dur":3782, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599656843, "dur":181, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599653008, "dur":4016, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599657098, "dur":3687, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__55.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599660802, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599657072, "dur":3939, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599661224, "dur":3474, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599664717, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599661173, "dur":3734, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hmkb5q84uqmc.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599665026, "dur":5771, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__6.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599671218, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599664979, "dur":6476, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvt1belsv8uz.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599671549, "dur":5145, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599676713, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599671498, "dur":5413, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qlh7yuo39bz.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599677225, "dur":1748, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__64.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599678991, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599677188, "dur":1997, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hjtq22u5el9s.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599679313, "dur":680, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752054599680010, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599679254, "dur":815, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4g4fb246gu9o.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599680169, "dur":5359, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599685548, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599680126, "dur":5628, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gbjz3jrhufsx.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599685839, "dur":5440, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__102.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599691295, "dur":174, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599685806, "dur":5664, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2vtz1tzlp5yt.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599691550, "dur":3699, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599695264, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599691518, "dur":3934, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bugq7q8skmw0.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599695742, "dur":6305, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__13.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599702066, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599695692, "dur":6622, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599702448, "dur":3935, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__5.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599706400, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599702409, "dur":4187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qggk6qrgvdze.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599706780, "dur":1180, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.DirectorModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752054599706744, "dur":1276, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6b8lrklf1hrp.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599708328, "dur":4500, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__8.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599712847, "dur":170, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599708282, "dur":4735, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1zy5u90sb95f.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599713124, "dur":137, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752054599713274, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599713080, "dur":251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b82i0fmwhe88.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599713493, "dur":3638, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__1.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599717155, "dur":300, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599713451, "dur":4004, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3rk5hkcdfkff.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599717490, "dur":248, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3rk5hkcdfkff.o" }}
,{ "pid":12345, "tid":21, "ts":1752054599717838, "dur":692, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Burst_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752054599718570, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.DirectorModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1752054599718718, "dur":334, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":21, "ts":1752054599719198, "dur":70012, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752054599789313, "dur":170, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599540255, "dur":51591, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599591850, "dur":2380, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599594230, "dur":21619, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599615854, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info" }}
,{ "pid":12345, "tid":22, "ts":1752054599615906, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599616091, "dur":223, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_4rfo.info" }}
,{ "pid":12345, "tid":22, "ts":1752054599616319, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599616387, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":22, "ts":1752054599616514, "dur":290, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":22, "ts":1752054599616807, "dur":4346, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599621155, "dur":257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752054599621413, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599621542, "dur":285, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752054599621921, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1752054599622207, "dur":312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":22, "ts":1752054599622519, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599623251, "dur":206, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.dll" }}
,{ "pid":12345, "tid":22, "ts":1752054599623458, "dur":9069, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599632575, "dur":6282, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599638874, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599632528, "dur":6535, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599639064, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599639411, "dur":6242, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__3.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599645671, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599639370, "dur":6526, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hq9xs8ifo9o5.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599645991, "dur":4972, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__26.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599650979, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599645949, "dur":5215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrq9v26xound.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599651263, "dur":1751, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1752054599653030, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599651227, "dur":1856, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599653164, "dur":2563, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__65.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599655748, "dur":167, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599653129, "dur":2786, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599655983, "dur":3937, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__18.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599659938, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599655963, "dur":4198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/btc00jpnpfwv.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599660413, "dur":3727, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599664159, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599660372, "dur":3991, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lj6ht2s4c6zd.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599664596, "dur":4189, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599668805, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599664555, "dur":4463, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c7i60zrowo1v.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599669138, "dur":3257, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599672412, "dur":175, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599669086, "dur":3501, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cqjhuhw737m8.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599672857, "dur":3370, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__6.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599676254, "dur":167, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599672828, "dur":3594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tal1f6v1rh3z.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599676620, "dur":3133, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Timeline.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599679777, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599676580, "dur":3409, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tg0fx1n1w1ut.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599680083, "dur":5231, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599685334, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599680044, "dur":5495, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f912yi01hhb2.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599685647, "dur":3214, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599688880, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599685600, "dur":3522, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p6sl71dxdw31.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599689299, "dur":4630, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599693948, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599689262, "dur":4908, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kzwq165kpxqi.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599694809, "dur":644, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1752054599695471, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599694763, "dur":762, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s8wzl21r6wdv.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599695635, "dur":2435, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__11.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599698089, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599695592, "dur":2720, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vclibqzzr333.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599698505, "dur":5789, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599704314, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599698452, "dur":6097, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8k8n05ob51ed.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599704638, "dur":6124, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599710781, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599704606, "dur":6406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zwfl6jeaz8l8.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599711226, "dur":1830, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1752054599713081, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599711175, "dur":1968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cc6dokwk1bcr.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599713547, "dur":834, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__32.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599714400, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599713502, "dur":1146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1awb9h9nex66.o" }}
,{ "pid":12345, "tid":22, "ts":1752054599716303, "dur":184, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599716936, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752054599717014, "dur":536, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__21.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599717583, "dur":1121, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__36.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599718791, "dur":397, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1752054599719242, "dur":70169, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599540298, "dur":51560, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599591863, "dur":2461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599594324, "dur":21532, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599615861, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Cinemachine-FeaturesChecked.txt_blar.info" }}
,{ "pid":12345, "tid":23, "ts":1752054599615913, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599616086, "dur":919, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info" }}
,{ "pid":12345, "tid":23, "ts":1752054599617006, "dur":4179, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599621193, "dur":1309, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1752054599622624, "dur":184, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":23, "ts":1752054599622993, "dur":569, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":23, "ts":1752054599623563, "dur":8981, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599632592, "dur":5111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__63.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599637722, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599632546, "dur":5389, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w3p2mtg8bxx9.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599638039, "dur":5109, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__20.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599643168, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599637999, "dur":5419, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/160kzx8hq6lj.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599643590, "dur":5029, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__78.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599648637, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599643554, "dur":5328, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ww4ib49dv6tl.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599649161, "dur":2995, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__18.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599652168, "dur":136, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599649124, "dur":3180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599652538, "dur":2785, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599655346, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599652489, "dur":3064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ulhaqys58uf2.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599655623, "dur":6309, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__9.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599661951, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599655591, "dur":6596, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0dcuv44u1v7h.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599662296, "dur":4313, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__14.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599666628, "dur":252, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599662256, "dur":4624, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2fcbro8s1h8l.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599667149, "dur":6057, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__4.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599673223, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599667107, "dur":6311, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tonwuvb1xski.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599673523, "dur":444, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Postprocessing.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1752054599673478, "dur":559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hzzs0hwl5jko.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599674113, "dur":4642, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__66.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599678772, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599674074, "dur":4897, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36gv2rp1lb2r.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599679089, "dur":2870, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__85.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599681985, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599679041, "dur":3193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvd93sq1nxoy.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599682237, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599682485, "dur":4447, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__3.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599686952, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599682433, "dur":4749, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ohmn0a84o3bk.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599687278, "dur":5983, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599693285, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599687246, "dur":6272, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8z7p351pgz6z.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599693608, "dur":5946, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599699579, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599693573, "dur":6212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yo7r87bsn5oh.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599700236, "dur":3535, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__72.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599703788, "dur":267, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599700187, "dur":3869, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iqmxf7rrbell.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599704334, "dur":4058, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__3.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599708407, "dur":160, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599704301, "dur":4267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4ssf61wkon6f.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599708648, "dur":3884, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599712552, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599708617, "dur":4161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mlw3ex27neg0.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599713577, "dur":2276, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__29.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599715872, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599713531, "dur":2555, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tuh2b1fry8rv.o" }}
,{ "pid":12345, "tid":23, "ts":1752054599717014, "dur":111, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__98.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599717143, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752054599717309, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__39.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599717393, "dur":436, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__39.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599718104, "dur":837, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599719049, "dur":322, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__2.cpp" }}
,{ "pid":12345, "tid":23, "ts":1752054599719372, "dur":70061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599540353, "dur":51525, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599591879, "dur":2434, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599594313, "dur":21551, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599615866, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_9x4w.info" }}
,{ "pid":12345, "tid":24, "ts":1752054599615955, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599616132, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599616228, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599616279, "dur":362, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.JSONSerializeModule-FeaturesChecked.txt_vjsc.info" }}
,{ "pid":12345, "tid":24, "ts":1752054599616645, "dur":58, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":24, "ts":1752054599616705, "dur":264, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":24, "ts":1752054599616974, "dur":4208, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599621183, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1752054599621451, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1752054599621618, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599622146, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1752054599622402, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599622735, "dur":307, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.DirectorModule.pdb" }}
,{ "pid":12345, "tid":24, "ts":1752054599623095, "dur":481, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":24, "ts":1752054599623577, "dur":8985, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599632602, "dur":5598, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599638218, "dur":169, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599632564, "dur":5823, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599638477, "dur":6844, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.Cinemachine__6.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599645335, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599638443, "dur":7089, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wiz2x7f2vocw.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599645621, "dur":3470, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599649108, "dur":199, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599645584, "dur":3724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599649489, "dur":2771, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599652276, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599649454, "dur":3069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xh4u7f3orcpz.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599652616, "dur":628, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":24, "ts":1752054599653262, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599652578, "dur":739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n6qmfnbbo4ko.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599653661, "dur":1828, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":24, "ts":1752054599653630, "dur":1910, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599655847, "dur":689, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752054599655808, "dur":771, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599656636, "dur":5060, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__8.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599661714, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599656617, "dur":5317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uo723pk33zgq.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599662068, "dur":7915, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599670003, "dur":158, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599662015, "dur":8146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i31sr3433hzj.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599670258, "dur":1327, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752054599671612, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599670221, "dur":1451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zl6xdwmh20f6.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599671836, "dur":3247, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__28.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599675106, "dur":159, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599671775, "dur":3491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8eeewr1tvkgc.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599675499, "dur":5405, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.RenderPipelines.Core.Runtime__6.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599680925, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599675453, "dur":5699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot0q86r8xntn.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599681252, "dur":5884, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp__9.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599687160, "dur":264, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599681210, "dur":6215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iza11ye77ffg.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599687540, "dur":4187, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__11.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599691737, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599687494, "dur":4441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/weayk7q21k6f.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599692170, "dur":1769, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VehiclesModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752054599693961, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599692135, "dur":1886, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hcfnlxn37dpc.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599694022, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599694197, "dur":5063, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__9.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599699277, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599694156, "dur":5330, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3ykurcul5mzh.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599699601, "dur":1559, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1752054599701177, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599699556, "dur":1677, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wvonircd98c.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599701234, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599701461, "dur":4137, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599705616, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599701418, "dur":4428, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mcvqv3cujwyv.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599706053, "dur":7246, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599713321, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599706003, "dur":7570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxs8b7zbmatr.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599713574, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599713874, "dur":2978, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__37.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599716876, "dur":274, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752054599713831, "dur":3320, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" }}
,{ "pid":12345, "tid":24, "ts":1752054599717495, "dur":109, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__99.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599717640, "dur":257, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599718041, "dur":354, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.Postprocessing.Runtime__1.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599718625, "dur":381, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599719074, "dur":321, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":24, "ts":1752054599719396, "dur":70045, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752054599814773, "dur":6978, "ph":"X", "name": "ProfilerWriteOutput" }
,